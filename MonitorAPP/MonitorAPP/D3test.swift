import SwiftUI
import AppKit
import Combine
import CoreGraphics // 添加这一行



// 更新后的 RichTextEditorViewModel
class RichTextEditorViewModel: NSObject, ObservableObject, URLSessionDataDelegate {
    @Published var richText: NSAttributedString = NSAttributedString(string: "") {
        didSet {
            hasUnsavedChanges = true
        }
    }

    @Published var lastSavedRichText: NSAttributedString = NSAttributedString(string: "") // 新增属性
    

    @Published var isLoaded: Bool = false
    @Published var loadError: String? = nil
    @Published var saveError: String? = nil
    @Published var progress: Double = 0.0 // 新增属性，用于显示加载进度

    // 新增属性，用于跟踪内容是否有未保存的更改
    @Published var hasUnsavedChanges: Bool = false

    // 添加用于字体大小和颜色更改的主题
    let fontSizeChange = PassthroughSubject<CGFloat, Never>()
    let fontColorChange = PassthroughSubject<NSColor, Never>()
    
    // 新增发布者，用于整体字体大小调整
    let globalFontSizeChange = PassthroughSubject<CGFloat, Never>()

    //用于将来的取消回退功能
    private var cancellables = Set<AnyCancellable>()

    var nodeID: Int
    let baseURL: String
    let authKey: String
    
    // 新增：需要 system_id 来加载和保存 rich_text_nodes
    // 由于富文本节点需要跟 system_id 绑定，请在创建 RichTextEditorViewModel 时传入 system_id
    var systemID: Int

    private var expectedContentLength: Int64 = 0
    private var receivedContentLength: Int64 = 0
    private var receivedData = Data()
    
    var loadCompletion: ((Bool, String?) -> Void)?
    
    init(nodeID: Int, baseURL: String, authKey: String, systemID: Int) {
        self.nodeID = nodeID
        self.baseURL = baseURL
        self.authKey = authKey
        self.systemID = systemID
        super.init()
    }

    func loadContent(completion: @escaping (Bool, String?) -> Void) {
        self.receivedData = Data()
        self.expectedContentLength = 0
        self.receivedContentLength = 0
        self.isLoaded = false
        self.loadError = nil

        
        guard let url = URL(string: "\(baseURL)/rich_text_nodes/\(nodeID)?system_id=\(systemID)") else {
            print("Invalid URL")
            DispatchQueue.main.async {
                self.loadError = "无效的URL。"
                self.isLoaded = true
                completion(false, self.loadError)
            }
            return
        }
        var request = URLRequest(url: url)
        request.setValue(authKey, forHTTPHeaderField: "Authorization") // 使用 viewModel 的 authKey 属性
        let session = URLSession(configuration: .default, delegate: self, delegateQueue: nil)
        let dataTask = session.dataTask(with: request)
        
        self.loadCompletion = completion
        
        dataTask.resume()
    }

    // MARK: - URLSessionDataDelegate 方法
    func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive response: URLResponse,
                    completionHandler: @escaping (URLSession.ResponseDisposition) -> Void) {
        expectedContentLength = response.expectedContentLength
        DispatchQueue.main.async {
            self.progress = 0.0
        }
        completionHandler(.allow)
    }

    func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive data: Data) {
        receivedData.append(data)
        receivedContentLength += Int64(data.count)
        let progressValue = expectedContentLength > 0 ? Double(receivedContentLength) / Double(expectedContentLength) : 0.0
        DispatchQueue.main.async {
            self.progress = progressValue
        }
    }

    // MARK: - URLSessionDataDelegate 方法
    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        if let error = error {
            print("loadContent error: \(error)")
            DispatchQueue.main.async {
                self.loadError = "oops!载入文本时候出错了，错误码：1235"
                self.isLoaded = true
                self.loadCompletion?(false, self.loadError)
                self.loadCompletion = nil
            }
            return
        }

        guard !receivedData.isEmpty else {
            print("loadContent returned no data.")
            DispatchQueue.main.async {
                self.loadError = "oops!载入文本时候出错了，错误码：1236"
                self.isLoaded = true
                self.loadCompletion?(false, self.loadError)
                self.loadCompletion = nil
            }
            return
        }

        // 新的解析方式：我们期望后端返回 { "rich_text_content": "<Base64-RTF字符串>" }
        if let json = try? JSONSerialization.jsonObject(with: receivedData, options: []),
           let dict = json as? [String: Any],
           let base64Str = dict["rich_text_content"] as? String {
            
            DispatchQueue.main.async {
                // 尝试把 Base64 -> Data -> RTF -> NSMutableAttributedString
                if let rtfData = Data(base64Encoded: base64Str),
                   let mutableAttributed = try? NSMutableAttributedString(
                       data: rtfData,
                       options: [.documentType: NSAttributedString.DocumentType.rtf],
                       documentAttributes: nil
                   ) {
                    self.richText = mutableAttributed
                } else {
                    // 如果解码失败，或者 RTF 解析失败，算是无效内容
                    print("无法解析为 RTF；可能后端还存的是HTML或者纯文本？")
                    let fallbackText = dict["rich_text_content"] as? String ?? ""
                    
                    // 如果想继续兼容老的HTML存储，可自行把 fallbackText 当HTML再试一次
                    // 这里就简单给个纯文字吧
                    let plainText = NSMutableAttributedString(string: fallbackText)
                    let newFont = NSFont(name: "PingFang SC", size: 14) ?? NSFont.systemFont(ofSize: 14)
                    plainText.addAttribute(.font, value: newFont, range: NSRange(location: 0, length: plainText.length))
                    
                    self.richText = plainText
                }

                self.lastSavedRichText = self.richText
                self.isLoaded = true
                self.hasUnsavedChanges = false
                self.loadCompletion?(true, nil)
                self.loadCompletion = nil
            }

        } else {
            print("loadContent could not parse content")
            DispatchQueue.main.async {
                self.loadError = "oops!载入文本时候出错了，错误码：1237"
                self.isLoaded = true
                self.loadCompletion?(false, self.loadError)
                self.loadCompletion = nil
            }
        }
    }
    
    
    // 修改后的 saveContent 方法，包含完成处理程序
    func saveContent(completion: @escaping (Bool, String?) -> Void) {
        guard let url = URL(string: "\(baseURL)/rich_text_nodes/\(nodeID)?system_id=\(systemID)") else {
            completion(false, "无效的保存URL。")
            return
        }
        var request = URLRequest(url: url)
        request.httpMethod = "PUT"
        request.setValue(authKey, forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // 1) 把 NSAttributedString -> RTF Data
        let rtfData = try? richText.data(
            from: NSRange(location: 0, length: richText.length),
            documentAttributes: [.documentType: NSAttributedString.DocumentType.rtf]
        )
        // 2) 再把 RTF Data 做 base64 编码
        let rtfBase64 = rtfData?.base64EncodedString() ?? ""

        // 3) 发给后端
        let body: [String: Any] = [
            "rich_text_content": rtfBase64,  // <- Base64形式
            "system_id": systemID
        ]
        request.httpBody = try? JSONSerialization.data(withJSONObject: body, options: [])

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                DispatchQueue.main.async {
                    self.saveError = "保存失败: \(error.localizedDescription)"
                    completion(false, self.saveError)
                }
                return
            }

            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    self.saveError = "保存失败: 无效的服务器响应。"
                    completion(false, self.saveError)
                }
                return
            }

            if (200...299).contains(httpResponse.statusCode) {
                DispatchQueue.main.async {
                    self.lastSavedRichText = self.richText
                    self.hasUnsavedChanges = false
                    self.saveError = nil
                    completion(true, nil)
                }
            } else if httpResponse.statusCode == 404 {
                // 若后端还没对应的记录，则去 createRichTextNode
                self.createRichTextNode(nodeID: self.nodeID, richTextContent: rtfBase64) { success, errorMessage in
                    if success {
                        // 创建成功后再尝试保存一次
                        self.saveContent(completion: completion)
                    } else {
                        DispatchQueue.main.async {
                            self.saveError = errorMessage
                            completion(false, self.saveError)
                        }
                    }
                }
            } else {
                DispatchQueue.main.async {
                    self.saveError = "保存失败: 服务器返回状态码 \(httpResponse.statusCode)。"
                    completion(false, self.saveError)
                }
            }
        }.resume()
    }

    // 回档功能
    func revertToLastSaved() {
        DispatchQueue.main.async {
            self.richText = self.lastSavedRichText
            self.hasUnsavedChanges = false
        }
    }

    private func createRichTextNode(nodeID: Int, richTextContent: String, completion: @escaping (Bool, String?) -> Void) {
        guard let url = URL(string: "\(baseURL)/rich_text_nodes") else {
            print("Invalid URL for createRichTextNode")
            completion(false, "无效的创建URL。")
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue(authKey, forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // 这里的 richTextContent 已经是 RTF 的 Base64 字串（与 saveContent 一致）
        let body: [String: Any] = [
            "node_id": nodeID,
            "rich_text_content": richTextContent,   // 这里应是base64 RTF
            "system_id": systemID
        ]

        request.httpBody = try? JSONSerialization.data(withJSONObject: body, options: [])

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("createRichTextNode error: \(error)")
                DispatchQueue.main.async {
                    self.saveError = "创建富文本节点失败: \(error.localizedDescription)"
                    completion(false, self.saveError)
                }
                return
            }
            guard let httpResponse = response as? HTTPURLResponse else {
                print("createRichTextNode failed: Invalid response")
                DispatchQueue.main.async {
                    self.saveError = "创建富文本节点失败: 无效的服务器响应。"
                    completion(false, self.saveError)
                }
                return
            }

            if (200...299).contains(httpResponse.statusCode) {
                print("createRichTextNode succeeded with status: \(httpResponse.statusCode)")
                DispatchQueue.main.async {
                    self.saveError = nil
                    completion(true, nil)
                }
            } else {
                print("createRichTextNode failed with status: \(httpResponse.statusCode)")
                DispatchQueue.main.async {
                    self.saveError = "创建富文本节点失败: 服务器返回状态码 \(httpResponse.statusCode)。"
                    completion(false, self.saveError)
                }
            }
        }.resume()
    }
}




//在 RichTextEditorView 中，利用 onAppear 调用 RichNSTextViewWrapper 的回调，以确保 NSTextView 获得焦点。
// RichTextEditorView 修改后的部分
struct RichTextEditorView: View {
    @StateObject var viewModel: RichTextEditorViewModel
    var nodeName: String
    var onClose: () -> Void

    @State private var windowController: EditorWindowController?
    @State private var showUnsavedAlert = false
    
    @State private var errorOverlay: NSHostingView<ErrorOverlay>? = nil
    
    init(nodeID: Int, baseURL: String, authKey: String, nodeName: String, onClose: @escaping () -> Void, systemID: Int) {
        _viewModel = StateObject(wrappedValue: RichTextEditorViewModel(nodeID: nodeID, baseURL: baseURL, authKey: authKey, systemID: systemID))
        self.nodeName = nodeName
        self.onClose = onClose
    }

    var body: some View {
        Color.clear
            .frame(width: 1, height: 1)
            .onAppear {
                DispatchQueue.main.async {
                    showEditorWindow()
                }
            }
            .allowsHitTesting(false)
    }
    
    
    private var editorMainContent: some View {
        ZStack {
            Color.clear
                .frame(width: 1, height: 1)
                .onAppear {
                    DispatchQueue.main.async {
                        showEditorWindow()
                    }
                }
                .onAppear {
                    DispatchQueue.main.async {
                        showEditorWindow()
                    }
                }
        }
    }
    private func showEditorWindow() {
        guard windowController == nil else { return }

        let uiState = EditorUIState()
        
        let contentView = EditorContent(
            viewModel: viewModel,
            nodeName: nodeName,
            onClose: {
                self.closeEditorWindow()
                self.windowController = nil
                onClose()
            },
            onForceClose: {
                windowController?.actuallyCloseWindow()
            },
            uiState: uiState
        )


        let hostingController = NSHostingController(rootView: contentView)
        let wc = EditorWindowController()
        wc.contentHostingController = hostingController
        wc.uiState = uiState
        wc.onWindowClose = {
            self.windowController = nil
        }
        wc.onCloseCallback = contentView.onClose
        
        wc.showWindow(nil)

        if let window = wc.window {
                window.title = nodeName
                window.center()
                NSApp.activate(ignoringOtherApps: true)

                DispatchQueue.main.async {
                    wc.window?.setFrameOrigin(NSPoint(x: 500, y: 200))
                    
                    let overlayHosting = NSHostingView(rootView: LoadingOverlay(message: "正在加载..."))
                    overlayHosting.frame = wc.window?.contentView?.bounds ?? .zero
                    wc.window?.contentView?.addSubview(overlayHosting)
                    
                    viewModel.loadContent { success, errorMessage in
                        DispatchQueue.main.async {
                            if success {
                                errorOverlay?.removeFromSuperview()
                                errorOverlay = nil
                                overlayHosting.removeFromSuperview()
                            } else {
                                overlayHosting.removeFromSuperview()
                                let newErrorOverlay = NSHostingView(rootView: ErrorOverlay(message: errorMessage ?? "未知错误") {
                                    self.errorOverlay?.removeFromSuperview()
                                    self.errorOverlay = nil
                                    
                                    let retryOverlay = NSHostingView(rootView: LoadingOverlay(message: "重新加载中..."))
                                    retryOverlay.frame = wc.window?.contentView?.bounds ?? .zero
                                    wc.window?.contentView?.addSubview(retryOverlay)
                                    viewModel.loadContent { success, err in
                                        DispatchQueue.main.async {
                                            retryOverlay.removeFromSuperview()
                                            if success {
                                                
                                            } else {
                                                let anotherErrorOverlay = NSHostingView(rootView: ErrorOverlay(
                                                    message: err ?? "仍然失败",
                                                    onRetry: {
                                                        self.errorOverlay?.removeFromSuperview()
                                                        self.errorOverlay = nil
                                                    }
                                                ))
                                                anotherErrorOverlay.frame = wc.window?.contentView?.bounds ?? .zero
                                                wc.window?.contentView?.addSubview(anotherErrorOverlay)
                                                self.errorOverlay = anotherErrorOverlay
                                            }
                                        }
                                    }
                                })

                                newErrorOverlay.frame = wc.window?.contentView?.bounds ?? .zero
                                wc.window?.contentView?.addSubview(newErrorOverlay)
                                self.errorOverlay = newErrorOverlay
                            }
                        }
                    }
                }
            }
            self.windowController = wc

    }
    
    
    
    private func closeEditorWindow() {
            if let wc = windowController {
                wc.close()
                windowController = nil
            }
        }

    struct EditorContent: View {
        @ObservedObject var viewModel: RichTextEditorViewModel
        var nodeName: String
        var onClose: () -> Void
        var onForceClose: () -> Void
        @ObservedObject var uiState: EditorUIState
        
        func promptUnsavedChanges() {
            DispatchQueue.main.async {
                self.uiState.showUnsavedChangesDialog = true
            }
        }
        
        var body: some View {
            ZStack(alignment: .bottomTrailing) {
                Color.clear

                VStack(spacing: 0) {
                    RichTextEditorToolbar(viewModel: viewModel, onClose: {
                        if viewModel.hasUnsavedChanges {
                            uiState.showUnsavedChangesDialog = true
                        } else {
                            onClose()
                        }
                    })
                    Divider()
                    RichNSTextViewWrapper(text: $viewModel.richText, viewModel: viewModel)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)

                }

                if uiState.showUnsavedChangesDialog {
                    CustomConfirmDialog(
                        title: "未保存的更改",
                        message: "您有未保存的更改，是否保存？",
                        confirmText: "保存",
                        destructiveText: "不保存",
                        onConfirm: {
                            viewModel.saveContent { success, errorMessage in
                                uiState.showUnsavedChangesDialog = false
                                if success {
                                    onClose()
                                } else {
                                    
                                }
                            }
                        },
                        onDestructive: {
                            uiState.showUnsavedChangesDialog = false
                            onForceClose()
                        },
                        onCancel: {
                            uiState.showUnsavedChangesDialog = false
                        }
                    )
                }
            }
        }
    }
}

// 添加一个 BlurView 来美化背景
struct BlurView: NSViewRepresentable {
    var style: NSVisualEffectView.BlendingMode = .behindWindow
    var material: NSVisualEffectView.Material = .windowBackground
    
    func makeNSView(context: Self.Context) -> NSVisualEffectView {
        let view = NSVisualEffectView()
        view.blendingMode = style
        view.material = material
        view.state = .active
        return view
    }
    func updateNSView(_ nsView: NSVisualEffectView, context: Context) {}
}




class EditorWindowController: NSWindowController, NSWindowDelegate {
    
    private var pendingWindowToClose: NSWindow?
    
    var uiState: EditorUIState?
    
    var onCloseCallback: (() -> Void)?
    
    var contentHostingController: NSHostingController<RichTextEditorView.EditorContent>? {
        didSet {
            if let hc = contentHostingController {
                let window = NSWindow(
                    contentRect: NSRect(x: 300, y: 300, width: 900, height: 900),
                    styleMask: [.titled, .resizable, .closable],
                    backing: .buffered,
                    defer: false
                )
                window.contentView = hc.view
                window.delegate = self
                self.window = window
            }
        }
    }
    
    var onWindowClose: (() -> Void)?

    func windowWillClose(_ notification: Notification) {
        onWindowClose?()
        onCloseCallback?()
        // 发布通知，告知主视图系统窗口已关闭
        NotificationCenter.default.post(name: .didCloseSystemWindow, object: nil)
    }
    
    func windowShouldClose(_ sender: NSWindow) -> Bool {
        if let viewModel = contentHostingController?.rootView.viewModel, viewModel.hasUnsavedChanges {
            DispatchQueue.main.async {
                self.pendingWindowToClose = sender
                self.uiState?.showUnsavedChangesDialog = true
            }
            return false
        }
        return true
    }
    
    func actuallyCloseWindow() {
        pendingWindowToClose?.performClose(nil)
        pendingWindowToClose = nil
    }
}

class EditorUIState: ObservableObject {
    @Published var showUnsavedChangesDialog = false
}


// 自定义对话框
struct CustomConfirmDialog: View {
    var title: String
    var message: String
    var confirmText: String
    var destructiveText: String
    var cancelText: String = "取消"

    var onConfirm: () -> Void
    var onDestructive: () -> Void
    var onCancel: () -> Void

    var body: some View {
        ZStack {
            Color.black.opacity(0.5)
                .ignoresSafeArea()

            VStack(spacing: 20) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.black)

                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.black)

                HStack(spacing: 20) {
                    Button(confirmText) {
                        onConfirm()
                    }
                    .buttonStyle(.bordered)

                    Button(destructiveText) {
                        onDestructive()
                    }
                    .buttonStyle(.borderedProminent)
                    .tint(.red)

                    Button(cancelText) {
                        onCancel()
                    }
                    .buttonStyle(.bordered)
                }
            }
            .padding(20)
            .background(Color.white)
            .cornerRadius(12)
            .shadow(radius: 10)
            .frame(maxWidth: 400)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
    }
}


struct RichTextEditorToolbar: View {
    @ObservedObject var viewModel: RichTextEditorViewModel
    @State private var selectedFontSize: CGFloat = 14
    @State private var selectedColor: Color = .black
    @State private var showRevertConfirmation = false
    @State private var showUnsavedChangesDialog = false
    var onClose: () -> Void

    var body: some View {
        ZStack {
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("编辑工具")
                        .font(.caption)
                        .foregroundColor(.gray)
                    HStack(spacing: 8) {
                        Button(action: {
                            viewModel.globalFontSizeChange.send(-1)
                        }) {
                            Image(systemName: "minus.circle")
                                .resizable()
                                .frame(width: 20, height: 20)
                                .foregroundColor(.blue)
                        }
                        Button(action: {
                            viewModel.globalFontSizeChange.send(1)
                        }) {
                            Image(systemName: "plus.circle")
                                .resizable()
                                .frame(width: 20, height: 20)
                                .foregroundColor(.blue)
                        }
                    }
                }

                Picker("字体大小：", selection: $selectedFontSize) {
                    ForEach([12,14,16,18,20,24,28,32], id: \.self) { size in
                        Text("\(size)").tag(CGFloat(size))
                    }
                }
                .pickerStyle(MenuPickerStyle())
                .onChange(of: selectedFontSize) { newSize in
                    viewModel.fontSizeChange.send(newSize)
                }

                ColorPicker("字体颜色", selection: $selectedColor, supportsOpacity: false)
                    .onChange(of: selectedColor) { newColor in
                        let nsColor = NSColor(newColor)
                        viewModel.fontColorChange.send(nsColor)
                    }

                Button(action: {
                    if viewModel.hasUnsavedChanges {
                        showRevertConfirmation = true
                    } else {
                        viewModel.revertToLastSaved()
                    }
                }) {
                    Image(systemName: "arrow.uturn.backward.circle")
                        .resizable()
                        .frame(width: 24, height: 24)
                        .foregroundColor(.orange)
                }
                .help("回档至上一次成功保存的版本")

                Spacer()

                Button(action: {
                    onClose()
                }) {
                    ZStack {
                        Circle()
                            .fill(Color.red)
                            .frame(width: 32, height: 32)
                        Text("×")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.white)
                    }
                }
                .help("关闭窗体")
            }
            .padding()
            .background(BlurView(style: .behindWindow, material: .appearanceBased))
            .cornerRadius(10)
            .shadow(radius: 5)

            
        }
        
        if showRevertConfirmation {
            CustomConfirmDialog(
                title: "回档确认",
                message: "确定要回档到上一次成功保存的版本吗？未保存的更改将丢失。",
                confirmText: "回档",
                destructiveText: "取消",
                onConfirm: {
                    viewModel.revertToLastSaved()
                    showRevertConfirmation = false
                },
                onDestructive: {
                    showRevertConfirmation = false
                },
                onCancel: {
                    showRevertConfirmation = false
                }
            )
        }

        if showUnsavedChangesDialog {
            CustomConfirmDialog(
                title: "未保存的更改",
                message: "您有未保存的更改，关闭后会丢失。\n是否保存？",
                confirmText: "保存",
                destructiveText: "不保存",
                onConfirm: {
                    viewModel.saveContent { success, errorMessage in
                        if success {
                            showUnsavedChangesDialog = false
                            onClose()
                        } else {
                            showUnsavedChangesDialog = false
                        }
                    }
                },
                onDestructive: {
                    showUnsavedChangesDialog = false
                    onClose()
                },
                onCancel: {
                    showUnsavedChangesDialog = false
                }
            )
        }
        
    }
}



struct RichNSTextViewWrapper: NSViewRepresentable {
    @Binding var text: NSAttributedString
    var viewModel: RichTextEditorViewModel

    func makeNSView(context: Context) -> NSScrollView {
        let scrollView = NSScrollView()
        scrollView.borderType = .noBorder
        scrollView.hasVerticalScroller = true
        scrollView.autohidesScrollers = false
        scrollView.scrollerStyle = .legacy

        let textView = NSTextView()
        textView.isRichText = true
        textView.isEditable = true
        textView.isSelectable = true
        textView.delegate = context.coordinator
        textView.allowsUndo = true

        textView.backgroundColor = NSColor.white
        textView.insertionPointColor = NSColor.black
        textView.font = NSFont(name: "PingFang SC", size: 14) ?? NSFont.systemFont(ofSize: 14)

        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .left

        textView.typingAttributes = [
            .foregroundColor: NSColor.black,
            .font: textView.font!,
            .paragraphStyle: paragraphStyle
        ]

        textView.drawsBackground = true
        textView.autoresizingMask = [.width, .height]
        textView.maxSize = NSSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude)
        textView.textContainer?.widthTracksTextView = true
        textView.textContainer?.heightTracksTextView = false
        textView.textContainerInset = NSSize(width: 10, height: 10)
        textView.textContainer?.lineFragmentPadding = 10

        scrollView.documentView = textView
        context.coordinator.textView = textView

        DispatchQueue.main.async {
            textView.window?.makeFirstResponder(textView)
            context.coordinator.didSetFirstResponder = true
        }

        return scrollView
    }

    func updateNSView(_ nsView: NSScrollView, context: Context) {
        guard let textView = nsView.documentView as? NSTextView else { return }

        // 建议直接使用 textStorage 来对比
        if let currentAttributed = textView.textStorage?.attributedSubstring(
            from: NSRange(location: 0, length: textView.textStorage?.length ?? 0)
        ),
           !currentAttributed.isEqual(to: text) {
            // ① 将后端返回的 RTF(富文本) 赋值给 textView
            textView.textStorage?.setAttributedString(text)
            
            // ② 不要再全文统一加段落/颜色，否则会覆盖掉原有样式
            // （以下代码已删除）
            //
            // let fullRange = NSRange(location: 0, length: text.length)
            // let mutableParagraphStyle = NSMutableParagraphStyle()
            // mutableParagraphStyle.alignment = .left
            // textView.textStorage?.addAttribute(.paragraphStyle, value: mutableParagraphStyle, range: fullRange)
            //
            // var newTypingAttributes = textView.typingAttributes
            // newTypingAttributes[.foregroundColor] = NSColor.black
            // newTypingAttributes[.paragraphStyle] = mutableParagraphStyle
            // textView.typingAttributes = newTypingAttributes
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, NSTextViewDelegate {
        var parent: RichNSTextViewWrapper
        weak var textView: NSTextView?
        var didSetFirstResponder = false
        private var cancellables = Set<AnyCancellable>()

        init(_ parent: RichNSTextViewWrapper) {
            self.parent = parent
            super.init()

            parent.viewModel.fontSizeChange
                .receive(on: DispatchQueue.main)
                .sink { [weak self] size in
                    self?.changeSelectedTextFontSize(size)
                }
                .store(in: &cancellables)

            parent.viewModel.fontColorChange
                .receive(on: DispatchQueue.main)
                .sink { [weak self] color in
                    self?.changeSelectedTextColor(color)
                }
                .store(in: &cancellables)

            parent.viewModel.globalFontSizeChange
                .receive(on: DispatchQueue.main)
                .sink { [weak self] delta in
                    self?.changeGlobalFontSize(delta: delta)
                }
                .store(in: &cancellables)
        }

        func textDidChange(_ notification: Notification) {
            textViewDidChange()
        }

        func textViewDidChange() {
            guard let textView = textView else { return }
            parent.text = NSAttributedString(attributedString: textView.textStorage ?? NSTextStorage())

        }


        func changeSelectedTextFontSize(_ size: CGFloat) {
            guard let textView = textView else { return }
            textView.undoManager?.beginUndoGrouping()
            let selectedRange = textView.selectedRange()
            if selectedRange.length > 0 {
                textView.textStorage?.enumerateAttribute(.font, in: selectedRange, options: []) { value, range, _ in
                    if let currentFont = value as? NSFont {
                        let newFont = NSFont(descriptor: currentFont.fontDescriptor, size: size) ?? NSFont.systemFont(ofSize: size)
                        textView.textStorage?.addAttribute(NSAttributedString.Key.font, value: newFont, range: range)
                    }
                }
            } else {
                var attributes = textView.typingAttributes
                if let currentFont = attributes[.font] as? NSFont {
                    let newFont = NSFont(descriptor: currentFont.fontDescriptor, size: size) ?? NSFont.systemFont(ofSize: size)
                    attributes[.font] = newFont
                } else {
                    attributes[.font] = NSFont.systemFont(ofSize: size)
                }
                textView.typingAttributes = attributes
            }
            textView.undoManager?.endUndoGrouping()
            textViewDidChange()
        }

        func changeGlobalFontSize(delta: CGFloat) {
            guard let textView = textView else { return }
            textView.undoManager?.beginUndoGrouping()
            let fullRange = NSRange(location: 0, length: textView.string.utf16.count)
            textView.textStorage?.enumerateAttribute(.font, in: fullRange, options: []) { value, range, _ in
                if let currentFont = value as? NSFont {
                    let newSize = max(currentFont.pointSize + delta, 1)
                    let newFont = NSFont(descriptor: currentFont.fontDescriptor, size: newSize) ?? NSFont.systemFont(ofSize: newSize)
                    textView.textStorage?.addAttribute(.font, value: newFont, range: range)
                }
            }
            textView.undoManager?.endUndoGrouping()
            textViewDidChange()
        }

        func changeSelectedTextColor(_ color: NSColor) {
            guard let textView = textView else { return }
            textView.undoManager?.beginUndoGrouping()
            let selectedRange = textView.selectedRange()
            if selectedRange.length > 0 {
                textView.textStorage?.addAttribute(.foregroundColor, value: color, range: selectedRange)
            } else {
                var attributes = textView.typingAttributes
                attributes[.foregroundColor] = color
                textView.typingAttributes = attributes
            }
            textView.undoManager?.endUndoGrouping()
            textViewDidChange()
        }
    }
}

struct GridPoint: Hashable {
    var xCoord: CGFloat
    var yCoord: CGFloat
}


struct GraphicObject: Identifiable, Equatable {
    let id = UUID()
    var databaseID: Int? = nil
    var position: CGPoint
    var size: CGSize
    var color: Color
    var shape: ShapeType
    var name: String?
    var nodeTypeID: Int

    static func ==(lhs: GraphicObject, rhs: GraphicObject) -> Bool {
        lhs.id == rhs.id
    }

    enum ShapeType {
        case rectangle
        case circle
    }
}


struct TextIconView: View {
    let nodeTypes: [NodeTypeItem]
    let typeID: Int
    var body: some View {
        ZStack {
            if let nodeType = nodeTypes.first(where: { $0.id == typeID }) {
                stylizedTextIcon(nodeType.name)
            } else {
                stylizedTextIcon("未知类型")
            }
        }
    }
    
    func stylizedTextIcon(_ text: String) -> some View {
        Text(text)
            .font(.system(size: 14, weight: .bold))
            .foregroundColor(.black)
            .multilineTextAlignment(.center)
            .padding(5)
    }
}

struct NodeView: View {
    let object: GraphicObject
    let pixelSize: CGSize
    let nodeTypes: [NodeTypeItem]
    
    @State private var mouseLocation: CGPoint = .zero
    
    @State private var isIconHovered: Bool = false
    @State private var isNameHovered: Bool = false
    
    let typeIconMap: [Int: String] = [
        1: "server.rack",
        2: "doc.text.fill",
        3: "point.3.filled.connected.trianglepath.dotted",
        4: "tray.and.arrow.down.fill",
        5: "desktopcomputer",
        6: "antenna.radiowaves.left.and.right",
        7: "network",
        8: "house.circle",
        9: "text.alignleft"
    ]

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    LinearGradient(gradient: Gradient(colors: [Color.blue, Color.purple]),
                                   startPoint: .topLeading,
                                   endPoint: .bottomTrailing)
                )
                .frame(width: pixelSize.width, height: pixelSize.height)
            
            HStack(spacing: 8) {
                nodeIcon
                    .resizable()
                    .scaledToFit()
                    .frame(height: pixelSize.height * 0.5)
                
                Text(object.name ?? "无名节点")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.white)
            }
        }
    }
    
    private var nodeIcon: Image {
        if let symbolName = typeIconMap[object.nodeTypeID] {
            return Image(systemName: symbolName)
        } else {
            return Image(systemName: "questionmark.circle")
        }
    }
}

struct ScalableOnHover: ViewModifier {
    @State private var isHovered = false
    let scale: CGFloat

    func body(content: Content) -> some View {
        content
            .scaleEffect(isHovered ? scale : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isHovered)
            .onHover { hovering in
                isHovered = hovering
            }
    }
}

extension View {
    func scalableOnHover(scale: CGFloat = 1.2) -> some View {
        self.modifier(ScalableOnHover(scale: scale))
    }
}


// MARK: - CoordinateSystem (保持不变)
struct CoordinateSystem: Hashable {
    let centerX: CGFloat
    let centerY: CGFloat
    let spacingInPixels: CGFloat
    let spacingInMM: CGFloat
    let pixelsPerMM: CGFloat
    let zoomScale: CGFloat
    let adjustedOffsetX: CGFloat
    let adjustedOffsetY: CGFloat

    func coordinateToPoint(x: CGFloat, y: CGFloat) -> CGPoint {
        let adjustedX = x - adjustedOffsetX
        let adjustedY = y - adjustedOffsetY
        let screenX = centerX + (adjustedX / spacingInMM) * spacingInPixels
        let screenY = centerY - (adjustedY / spacingInMM) * spacingInPixels
        return CGPoint(x: screenX, y: screenY)
    }

    func sizeInPixels(mmSize: CGSize) -> CGSize {
        let scaleFactor = 1 / zoomScale
        let width = mmSize.width * pixelsPerMM * scaleFactor
        let height = mmSize.height * pixelsPerMM * scaleFactor
        return CGSize(width: width, height: height)
    }

    func pointToCoordinate(screenX: CGFloat, screenY: CGFloat) -> CGPoint {
        let dxPixels = screenX - centerX
        let dyPixels = centerY - screenY
        let dxMM = dxPixels / (spacingInPixels / spacingInMM)
        let dyMM = dyPixels / (spacingInPixels / spacingInMM)
        let xCoord = dxMM + adjustedOffsetX
        let yCoord = dyMM + adjustedOffsetY
        return CGPoint(x: xCoord, y: yCoord)
    }
}

struct NodeTypeItem: Identifiable {
    let id: Int
    let name: String
    let description: String?
    // ★ 新增字段
    let colorHex: String?
}

struct Connection: Identifiable {
    let id: Int
    let fromNodeID: Int
    let toNodeID: Int
    let connectionType: String?
}


// MARK: - AnimatedGridView (保持不变)
struct AnimatedGridView: View, Animatable {
    var adjustedOffsetX: CGFloat
    var adjustedOffsetY: CGFloat

    var animatableData: AnimatablePair<CGFloat, CGFloat> {
        get { AnimatablePair(adjustedOffsetX, adjustedOffsetY) }
        set {
            adjustedOffsetX = newValue.first
            adjustedOffsetY = newValue.second
        }
    }

    let width: CGFloat
    let height: CGFloat
    let centerX: CGFloat
    let centerY: CGFloat
    let spacingInPixels: CGFloat
    let spacingInMM: CGFloat
    let pixelsPerMM: CGFloat
    let zoomScale: CGFloat
    let lineSpacingMM: Double
    let showDashedLines: Bool
    let dashedLineOpacity: Double
    let showCoordinateLabels: Bool
    let coordinateLabelOpacity: Double

    var body: some View {
        let coordinateSystem = CoordinateSystem(
            centerX: centerX,
            centerY: centerY,
            spacingInPixels: spacingInPixels,
            spacingInMM: spacingInMM,
            pixelsPerMM: pixelsPerMM,
            zoomScale: zoomScale,
            adjustedOffsetX: adjustedOffsetX,
            adjustedOffsetY: adjustedOffsetY
        )

        let xMin = adjustedOffsetX - (centerX / spacingInPixels) * spacingInMM
        let xMax = adjustedOffsetX + ((width - centerX) / spacingInPixels) * spacingInMM
        let yMin = adjustedOffsetY - (centerY / spacingInPixels) * spacingInMM
        let yMax = adjustedOffsetY + ((height - centerY) / spacingInPixels) * spacingInMM

        let iMin = Int(floor(xMin / spacingInMM))
        let iMax = Int(ceil(xMax / spacingInMM))
        let jMin = Int(floor(yMin / spacingInMM))
        let jMax = Int(ceil(yMax / spacingInMM))

        let verticalLines: [CGFloat] = Array(iMin...iMax).map { CGFloat($0) * spacingInMM }
        let horizontalLines: [CGFloat] = Array(jMin...jMax).map { CGFloat($0) * spacingInMM }

        let coordinatePoints: [GridPoint] = {
            var points = [GridPoint]()
            for i in iMin...iMax {
                for j in jMin...jMax {
                    points.append(GridPoint(xCoord: CGFloat(i)*spacingInMM, yCoord: CGFloat(j)*spacingInMM))
                }
            }
            return points
        }()

        return ZStack {
            if showDashedLines {
                ForEach(verticalLines, id: \.self) { xCoord in
                    let xPosition = coordinateSystem.coordinateToPoint(x: xCoord, y: 0).x
                    Path { path in
                        path.move(to: CGPoint(x: xPosition, y: 0))
                        path.addLine(to: CGPoint(x: xPosition, y: height))
                    }
                    .stroke(style: StrokeStyle(lineWidth: 0.5, dash: [5]))
                    .foregroundColor(Color.blue.opacity(dashedLineOpacity))
                }

                ForEach(horizontalLines, id: \.self) { yCoord in
                    let yPosition = coordinateSystem.coordinateToPoint(x: 0, y: yCoord).y
                    Path { path in
                        path.move(to: CGPoint(x: 0, y: yPosition))
                        path.addLine(to: CGPoint(x: width, y: yPosition))
                    }
                    .stroke(style: StrokeStyle(lineWidth: 0.5, dash: [5]))
                    .foregroundColor(Color.blue.opacity(dashedLineOpacity))
                }
            }

            if showCoordinateLabels {
                ForEach(coordinatePoints, id: \.self) { point in
                    let position = coordinateSystem.coordinateToPoint(x: point.xCoord, y: point.yCoord)
                    Text("(\(Int(point.xCoord)), \(Int(point.yCoord)))")
                        .font(.system(size: 10))
                        .foregroundColor(Color.blue.opacity(coordinateLabelOpacity))
                        .position(x: position.x + 15, y: position.y)
                }
            }
        }
    }
}


struct ConnectionShape: Shape {
    var from: CGPoint
    var to: CGPoint

    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: from)
        path.addLine(to: to)
        return path
    }
}





struct VisualEffectBlurView: NSViewRepresentable {
    var material: NSVisualEffectView.Material
    var blendingMode: NSVisualEffectView.BlendingMode
    
    func makeNSView(context: Context) -> NSVisualEffectView {
        let view = NSVisualEffectView()
        view.material = material
        view.blendingMode = blendingMode
        view.state = .active
        return view
    }
    
    func updateNSView(_ nsView: NSVisualEffectView, context: Context) {
        nsView.material = material
        nsView.blendingMode = blendingMode
    }
}

/// 一个更华丽的 LoadingOverlay
struct LoadingOverlay: View {
    var message: String
    
    /// 控制旋转动画的状态
    @State private var isRotating = false
    
    var body: some View {
        ZStack {
            // 半透明毛玻璃背景
            VisualEffectBlurView(material: .hudWindow, blendingMode: .behindWindow)
                .edgesIgnoringSafeArea(.all)
                .opacity(0.7)

            // 内容
            VStack(spacing: 20) {
                // 渐变圆环
                ZStack {
                    Circle()
                        .trim(from: 0, to: 0.7) // 只画70%的环
                        .stroke(
                            AngularGradient(
                                gradient: Gradient(colors: [.pink, .purple, .blue, .pink]),
                                center: .center
                            ),
                            style: StrokeStyle(lineWidth: 8, lineCap: .round)
                        )
                        .frame(width: 50, height: 50)
                        .rotationEffect(.degrees(isRotating ? 360 : 0))
                        .animation(
                            .linear(duration: 1.0)
                            .repeatForever(autoreverses: false),
                            value: isRotating
                        )
                }
                .onAppear {
                    isRotating = true
                }

                // 文字说明
                Text(message)
                    .foregroundColor(.white)
                    .font(.title3.weight(.semibold))
                    .multilineTextAlignment(.center)
                    .padding(.top, 8)
            }
            .padding(30)
            .background(
                Color.black.opacity(0.2)
                    .blur(radius: 5) // 再加一层模糊，可自行调整
            )
            .clipShape(RoundedRectangle(cornerRadius: 16, style: .continuous))
            .shadow(color: Color.black.opacity(0.4), radius: 10, x: 0, y: 5)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}




struct ErrorOverlay: View {
    var message: String
    var onRetry: () -> Void
    var onCancel: (() -> Void)? = nil

    var body: some View {
        ZStack {
            Color.black.opacity(0.5).ignoresSafeArea()
            VStack(spacing: 20) {
                Text("错误")
                    .font(.headline)
                    .foregroundColor(.red)
                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.white)
                
                HStack {
                    Button("重试") {
                        onRetry()
                    }
                    .buttonStyle(.borderedProminent)
                    .tint(.green)
                    
                    if let onCancel = onCancel {
                        Button("取消") {
                            onCancel()
                        }
                        .buttonStyle(.bordered)
                        .tint(.red)
                    }
                }
            }
            .padding(20)
            .background(Color.gray.opacity(0.8))
            .cornerRadius(12)
        }
    }
}

struct DynamicColorButtonStyle: ButtonStyle {
    @Binding var hue: Double
    
    func makeBody(configuration: Configuration) -> some View {
        let colors = stride(from: 0.0, to: 0.5, by: 0.1).map { offset in
            Color(hue: (hue + offset).truncatingRemainder(dividingBy: 1.0),
                  saturation: 0.8,
                  brightness: 0.9)
        }
        
        return configuration.label
            .font(.title.bold())
            .foregroundColor(.white)
            .frame(width: 200, height: 60)
            .background(
                LinearGradient(gradient: Gradient(colors: colors),
                               startPoint: .topLeading, endPoint: .bottomTrailing)
                .clipShape(RoundedRectangle(cornerRadius: 12))
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: configuration.isPressed)
    }
}

struct MonochromeButtonStyle: ButtonStyle {
    let isSquare: Bool

    func makeBody(configuration: Configuration) -> some View {
        let baseSize: CGFloat = isSquare ? 60 : 200
        
        return configuration.label
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.black)
            .frame(width: baseSize, height: 60)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.white)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .strokeBorder(Color.black, lineWidth: 2)
            )
            .clipShape(RoundedRectangle(cornerRadius: 10))
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.9 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}


class GlobalTimerModel: ObservableObject {
    @Published var timePhase: Double = 0.0
    var timer: Timer?
    
    init() {
        timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            self.timePhase += 0.01
        }
    }
}



// NodeSystemViewModel 原有无须修改的地方保持不变，但在创建和加载节点时加入 system_id
class NodeSystemViewModel: ObservableObject {
    @Published var objects: [GraphicObject] = []
    @Published var connections: [Connection] = []
    @Published var selectedNodeID: Int? = nil
    @Published var draggingNodeID: Int? = nil
    @Published var originalNodePos: CGPoint = .zero
    
    var baseURL: String
    var authKey: String
    
    // 新增：需要获取 system_id
    var systemID: Int
    @Published var redrawToken = UUID()
    
    init(baseURL: String, authKey: String, systemID: Int) {
        self.baseURL = baseURL
        self.authKey = authKey
        self.systemID = systemID
    }

   
    
    /// 删除连接
    func deleteConnectionInBackend(_ connectionID: Int, completion: @escaping (Bool) -> Void) {
        guard let url = URL(string: "\(baseURL)/connections/\(connectionID)?system_id=\(systemID)") else {
            print("无效的连接删除URL。")
            completion(false)
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.setValue(authKey, forHTTPHeaderField: "Authorization")

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("删除连接时出错: \(error)")
                completion(false)
                return
            }
            guard let httpResponse = response as? HTTPURLResponse else {
                print("删除连接失败: 无效的服务器响应。")
                completion(false)
                return
            }

            if httpResponse.statusCode == 200 {
                // 从本地的 connections 数组中移除
                DispatchQueue.main.async {
                    if let idx = self.connections.firstIndex(where: { $0.id == connectionID }) {
                        self.connections.remove(at: idx)
                    }
                }
                completion(true)
            } else {
                print("删除连接失败，状态码: \(httpResponse.statusCode)")
                completion(false)
            }
        }.resume()
    }
    
    func findNodeAtPosition(_ pos: CGPoint, coordinateSystem: CoordinateSystem) -> GraphicObject? {
        for obj in objects {
            let objPos = coordinateSystem.coordinateToPoint(x: obj.position.x, y: obj.position.y)
            let objSize = coordinateSystem.sizeInPixels(mmSize: obj.size)
            
            var hitRect = CGRect(
                x: objPos.x - objSize.width / 2,
                y: objPos.y - objSize.height / 2,
                width: objSize.width,
                height: objSize.height
            )
            
            if obj.nodeTypeID != 9, let nodeName = obj.name, !nodeName.isEmpty {
                let labelHeight = objSize.height * 0.2
                hitRect = hitRect.insetBy(dx: -10, dy: -10).union(CGRect(x: hitRect.origin.x - 10, y: hitRect.origin.y + hitRect.height, width: hitRect.width + 20, height: labelHeight + 10))
            }

            if hitRect.contains(pos) {
                return obj
            }
        }
        return nil
    }

    func findNodeByPosition(_ logicPos: CGPoint) -> GraphicObject? {
        var nearest: GraphicObject? = nil
        var minDist: CGFloat = CGFloat.greatestFiniteMagnitude
        for obj in objects {
            let dx = obj.position.x - logicPos.x
            let dy = obj.position.y - logicPos.y
            let dist = sqrt(dx*dx + dy*dy)
            if dist < minDist {
                minDist = dist
                nearest = obj
            }
        }
        return nearest
    }

    func onNodeClicked(_ node: GraphicObject) {
        selectedNodeID = node.databaseID
    }

    func onNodeDragStart(_ node: GraphicObject) {
        draggingNodeID = node.databaseID
        originalNodePos = node.position
    }

    func onNodeDragChanged(_ translation: CGSize, coordinateSystem: CoordinateSystem, node: GraphicObject, onUpdateNodePosition: (Int, CGPoint, CGSize) -> Void) {
        let deltaXMM = translation.width * (coordinateSystem.spacingInMM / coordinateSystem.spacingInPixels)
        let deltaYMM = -translation.height * (coordinateSystem.spacingInMM / coordinateSystem.spacingInPixels)

        if let idx = objects.firstIndex(where: { $0.databaseID == node.databaseID }) {
            objects[idx].position = CGPoint(
                x: originalNodePos.x + deltaXMM,
                y: originalNodePos.y + deltaYMM
            )
            
            // 显式触发对象变化通知，让UI重绘
            objectWillChange.send()
        }
        
        //拖拽有连线的节点后，强制canvas重新更新连线
        DispatchQueue.main.async {
            self.redrawToken = UUID()
        }
        
        
    }

    func onNodeDragEnd(_ node: GraphicObject, onUpdateNodePosition: (Int, CGPoint, CGSize) -> Void) {
        if let idx = objects.firstIndex(where: { $0.databaseID == node.databaseID }),
           let oid = objects[idx].databaseID {
            onUpdateNodePosition(oid, objects[idx].position, objects[idx].size)
        }
        draggingNodeID = nil
    }

    func deleteNode(node: GraphicObject, completion: @escaping ()->Void) {
        guard let nodeId = node.databaseID else {
            print("节点ID不存在，无法删除。")
            completion()
            return
        }
        
        guard let url = URL(string: "\(baseURL)/nodes/\(nodeId)?system_id=\(systemID)") else {
            print("无效的URL。")
            completion()
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.setValue(authKey, forHTTPHeaderField: "Authorization")
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("删除节点时出错: \(error)")
                    completion()
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    print("无法获取HTTP响应。")
                    completion()
                    return
                }
                
                if httpResponse.statusCode == 200 {
                    if let index = self.objects.firstIndex(where: { $0.databaseID == nodeId }) {
                        self.objects.remove(at: index)
                    }
                    self.connections.removeAll { $0.fromNodeID == nodeId || $0.toNodeID == nodeId }
                    completion()
                } else {
                    if let data = data,
                       let errorResponse = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                       let errorMessage = errorResponse["error"] as? String {
                        print("删除节点失败: \(errorMessage)")
                    } else {
                        print("删除节点失败，状态码: \(httpResponse.statusCode)")
                    }
                    completion()
                }
            }
        }.resume()
    }

    func createNode(x: CGFloat, y: CGFloat, name: String, typeID: Int, width: Double, height: Double, completion: @escaping ()->Void) {
        guard let url = URL(string: "\(baseURL)/nodes") else { completion(); return }
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue(authKey, forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let body: [String: Any] = [
            "system_id": systemID,
            "name": name,
            "node_type_id": typeID,
            "x": Double(x),
            "y": Double(y),
            "width": width,
            "height": height,
            "rotation": 0.0,
            "z_index": 0
        ]

        request.httpBody = try? JSONSerialization.data(withJSONObject: body, options: [])

        URLSession.shared.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                defer { completion() }
                if let error = error {
                    print("createNode error: \(error)")
                    return
                }
                // 无需额外处理，仅在加载时刷新
            }
        }.resume()
    }

    func createConnection(from: Int, to: Int) {
        guard let url = URL(string: "\(baseURL)/connections") else { return }
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue(authKey, forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let body: [String: Any] = [
            "system_id": systemID,
            "from_node_id": from,
            "to_node_id": to,
            "connection_type": "ethernet"
        ]

        request.httpBody = try? JSONSerialization.data(withJSONObject: body, options: [])

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("createConnection error: \(error)")
                return
            }
            guard let data = data else {
                print("createConnection no data returned.")
                return
            }
            if let resp = response as? HTTPURLResponse {
                print("createConnection HTTP status: \(resp.statusCode)")
            }

            if let json = try? JSONSerialization.jsonObject(with: data, options: []),
               let dict = json as? [String: Any],
               let id = dict["id"] as? Int,
               let fromID = dict["from_node_id"] as? Int,
               let toID = dict["to_node_id"] as? Int,
               let connType = dict["connection_type"] as? String {
                DispatchQueue.main.async {
                    let newConnection = Connection(id: id, fromNodeID: fromID, toNodeID: toID, connectionType: connType)
                    self.connections.append(newConnection)
                }
            } else {
                DispatchQueue.main.async {
                    let tempID = (self.connections.map { $0.id }.max() ?? 0) + 1
                    let newConnection = Connection(id: tempID, fromNodeID: from, toNodeID: to, connectionType: "ethernet")
                    self.connections.append(newConnection)
                }
            }

        }.resume()
    }
    
    func updateNodePositionInBackend(_ id: Int, _ position: CGPoint, _ size: CGSize) {
        guard let url = URL(string: "\(baseURL)/nodes/\(id)") else { return }
        var request = URLRequest(url: url)
        request.httpMethod = "PUT"
        request.setValue(authKey, forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        let body: [String: Any] = [
            "system_id": systemID,
            "x": Double(position.x),
            "y": Double(position.y),
            "width": Double(size.width),
            "height": Double(size.height)
        ]

        request.httpBody = try? JSONSerialization.data(withJSONObject: body, options: [])

        URLSession.shared.dataTask(with: request) { _, response, error in
            if let error = error {
                print("updateNodePositionInBackend error: \(error)")
            }
        }.resume()
    }
}



// NodeSystemView
// NodeSystemView，以下代码由o1 pro mode提供
// NodeSystemView
// NodeSystemView
// NodeSystemView (带拉线功能完整示例)
// 在使用本代码前，请确保：
// 1. 你已经有 NodeSystemViewModel、GraphicObject、Connection、CoordinateSystem、NodeTypeItem 等结构。
// 2. 你在使用 NodeSystemView 时为 selectedRichTextNodeID 传入一个 @Binding Int? 变量，以便富文本编辑器正常显示。
// 3. 本示例中为了简单，在节点上显示两个锚点（左右各一个），当点击锚点时开始拉线，拖动鼠标以创建连接。
// NodeConnectionLinesView.swift
import SwiftUI

struct NodeConnectionLinesView: View {
    let viewModel: NodeSystemViewModel
    let coordinateSystem: CoordinateSystem
    let shouldDrawConnections: Bool

    var body: some View {
        Canvas { context, size in
            guard shouldDrawConnections else { return }
            for conn in viewModel.connections {
                guard let fromObj = viewModel.objects.first(where: { $0.databaseID == conn.fromNodeID }),
                      let toObj = viewModel.objects.first(where: { $0.databaseID == conn.toNodeID }) else {
                    continue
                }

                let fromPoint = coordinateSystem.coordinateToPoint(x: fromObj.position.x, y: fromObj.position.y)
                let toPoint = coordinateSystem.coordinateToPoint(x: toObj.position.x, y: toObj.position.y)

                let fromPixelSize = coordinateSystem.sizeInPixels(mmSize: fromObj.size)
                let toPixelSize = coordinateSystem.sizeInPixels(mmSize: toObj.size)

                let direction = CGPoint(x: toPoint.x - fromPoint.x, y: toPoint.y - fromPoint.y)
                let adjustedFrom = intersectionWithRect(center: fromPoint, width: fromPixelSize.width, height: fromPixelSize.height, direction: direction)
                let adjustedTo = intersectionWithRect(center: toPoint, width: toPixelSize.width, height: toPixelSize.height, direction: CGPoint(x: -direction.x, y: -direction.y))

                let baseLineWidth: CGFloat = 2.0
                let lineWidth = max(1.0, baseLineWidth / coordinateSystem.zoomScale)

                let involvesRichTextNode = (fromObj.nodeTypeID == 9 || toObj.nodeTypeID == 9)

                var path = Path()
                path.move(to: adjustedFrom)
                path.addLine(to: adjustedTo)

                let style = StrokeStyle(lineWidth: lineWidth, lineCap: .round, lineJoin: .round, dash: [4,6])

                if involvesRichTextNode {
                    context.stroke(path, with: .color(Color.blue), style: style)
                } else {
                    let lineGradient = Gradient(colors: [Color.blue.opacity(0.7), Color.purple.opacity(0.7)])
                    context.stroke(
                        path,
                        with: .linearGradient(
                            lineGradient,
                            startPoint: adjustedFrom,
                            endPoint: adjustedTo
                        ),
                        style: style
                    )
                }
            }
        }
    }

    func intersectionWithRect(center: CGPoint, width: CGFloat, height: CGFloat, direction: CGPoint) -> CGPoint {
        let halfW = width / 2.0
        let halfH = height / 2.0
        let d = sqrt(direction.x * direction.x + direction.y * direction.y)
        if d < 0.0001 {
            return center
        }
        let dx = direction.x / d
        let dy = direction.y / d
        let tx = halfW / abs(dx)
        let ty = halfH / abs(dy)
        let t = min(tx, ty)
        return CGPoint(x: center.x + dx * t, y: center.y + dy * t)
    }
}



// NodeAnchorsView.swift
// NodeAnchorsView.swift
import SwiftUI

struct NodeAnchorsView: View {
    let objectPosition: CGPoint
    let pixelSize: CGSize
    let anchorSize: CGFloat
    let anchorOffset: CGFloat

    // 新增绑定与参数，将连接拖拽相关的状态和方法从上级传入
    @Binding var isDraggingConnection: Bool
    @Binding var connectionStartNodeID: Int?
    @Binding var connectionStartPos: CGPoint
    @Binding var connectionCurrentMousePos: CGPoint
    var highlightedNodeID: Int?
    let onCreateConnection: (Int, Int) -> Void
    let endConnectionDrag: () -> Void
    let beginConnectionDrag: (GraphicObject, CGPoint) -> Void
    let object: GraphicObject

    // 用来防止鼠标点击锚点后产生斜线
    @Binding var hasStartedLineDrag: Bool

    // ========== 新增部分：判断是否已存在连线 & 删除连线的回调 ==========
    /// 用于判断这两个节点之间是否已存在连线
    let doesConnectionExist: (Int, Int) -> Bool
    /// 用于删除已存在的连线并刷新视图
    let onDeleteConnection: (Int, Int) -> Void

    // ========== 新增部分：3秒计时器相关状态 ==========
    @State private var connectionRemovalTimer: Timer? = nil
    @State private var removalCandidateConnection: (Int, Int)? = nil

    var body: some View {
        Group {
            anchorView(offsetX: -(pixelSize.width/2 + anchorOffset), offsetY: 0)
            anchorView(offsetX: (pixelSize.width/2 + anchorOffset), offsetY: 0)
        }
    }

    func anchorView(offsetX: CGFloat, offsetY: CGFloat) -> some View {
        Circle()
            .fill(Color.blue.opacity(0.8))
            .frame(width: anchorSize, height: anchorSize)
            .overlay(
                Circle().stroke(Color.white, lineWidth: 2)
            )
            .shadow(radius: 2)
            .offset(x: offsetX, y: offsetY)
            .gesture(
                DragGesture(minimumDistance: 5)
                    .onChanged { value in
                        print("=== [onChanged] Entered, isDraggingConnection=\(isDraggingConnection), hasStartedLineDrag=\(hasStartedLineDrag), highlightedNodeID=\(String(describing: highlightedNodeID))")

                        // 若尚未开始拉线，则初始化拉线
                        if !isDraggingConnection {
                            let startPos = CGPoint(x: objectPosition.x + offsetX, y: objectPosition.y + offsetY)
                            print("... Begin new drag, call beginConnectionDrag")
                            beginConnectionDrag(object, startPos)
                        }

                        // 只要拖动超过5像素后，就显示线条
                        if !hasStartedLineDrag {
                            print("... hasStartedLineDrag=false -> set hasStartedLineDrag=true & show line")
                            connectionCurrentMousePos = connectionStartPos
                            hasStartedLineDrag = true
                        } else {
                            connectionCurrentMousePos = CGPoint(
                                x: connectionStartPos.x + value.translation.width,
                                y: connectionStartPos.y + value.translation.height
                            )
                        }

                        // ========== 下面是新增的 3 秒删除逻辑 ==========
                        // 如果鼠标正悬停在另一个节点上并且这两个节点之间已存在连线
                        if let startID = connectionStartNodeID,
                           let targetID = highlightedNodeID,
                           doesConnectionExist(startID, targetID) {

                            print("... doesConnectionExist(\(startID), \(targetID)) = true")

                            // 如果当前候选连接与上一次记录的不同，则重新启动计时器
                            if removalCandidateConnection?.0 != startID
                                || removalCandidateConnection?.1 != targetID
                            {
                                print("... removalCandidateConnection changed, resetting timer")

                                // 先取消上一次的计时
                                connectionRemovalTimer?.invalidate()
                                connectionRemovalTimer = nil

                                // 记录新的候选连接
                                removalCandidateConnection = (startID, targetID)

                                // 启动3秒计时器
                                print("Begin 3s timer for removing connection from \(startID) to \(targetID)")
                                connectionRemovalTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
                                    print("Timer fired for removing connection from \(startID) to \(targetID)")
                                    print("isDraggingConnection=\(isDraggingConnection), highlightedNodeID=\(String(describing: highlightedNodeID))")

                                    // 3秒后，若仍在拖拽状态且高亮节点没变，并且未松手
                                    // 则删除这条连线
                                    if isDraggingConnection,
                                       let currentHighlight = highlightedNodeID,
                                       currentHighlight == targetID
                                    {
                                        print("Deleting connection from \(startID) to \(targetID) ...")
                                        onDeleteConnection(startID, targetID)
                                    } else {
                                        print("Conditions not met, won't delete.")
                                    }
                                }
                            } else {
                                // 仍在同一个连接上，不重复重置timer
                                print("... still same candidateConnection (\(startID), \(targetID)), do nothing")
                            }

                        } else {
                            // 如果鼠标离开/未高亮 或者 并不存在连线，则取消计时
                            print("... doesConnectionExist=false or no highlight, reset timer/candidate")
                            connectionRemovalTimer?.invalidate()
                            connectionRemovalTimer = nil
                            removalCandidateConnection = nil
                        }
                    }
                    .onEnded { value in
                        print("=== [onEnded] Drag ended, isDraggingConnection=\(isDraggingConnection). Cancel timer if any.")

                        // 拖拽结束后，先取消任何计时
                        connectionRemovalTimer?.invalidate()
                        connectionRemovalTimer = nil
                        removalCandidateConnection = nil

                        // 若结束时落在另一个节点上，且这两个节点之间不存在连线，则新建连线
                        if let targetNodeID = highlightedNodeID,
                           let startID = connectionStartNodeID,
                           !doesConnectionExist(startID, targetNodeID) {
                            print("... onEnded -> createConnection(\(startID), \(targetNodeID))")
                            onCreateConnection(startID, targetNodeID)
                        }
                        endConnectionDrag()
                    }
            )
    }
}

// NodeItemView.swift
// NodeItemView.swift (更新后的代码)
// NodeItemView.swift (更新后的代码)
// NodeItemView.swift (更新后的代码)
// NodeItemView.swift (更新后的代码)
// NodeItemView.swift
struct NodeItemView: View {
    let object: GraphicObject
    let coordinateSystem: CoordinateSystem
    let selectedNodeID: Int?
    let anchorSize: CGFloat
    let anchorOffset: CGFloat
    @Binding var isDraggingConnection: Bool
    @Binding var connectionStartNodeID: Int?
    @Binding var connectionStartPos: CGPoint
    let onNodeClicked: (GraphicObject) -> Void
    let onDeleteNode: (GraphicObject) -> Void
    let onUpdateNodePosition: (Int, CGPoint, CGSize) -> Void
    let selectedRichTextNodeID: Binding<Int?>
    let viewModel: NodeSystemViewModel

    // 这里新增一个属性，用于获取所有 nodeTypes，从而根据名称来判断是否是富文本节点
    let nodeTypes: [NodeTypeItem]

    @Binding var highlightedNodeID: Int?
    @Binding var connectionCurrentMousePos: CGPoint
    let onCreateConnection: (Int, Int) -> Void
    let endConnectionDrag: () -> Void

    @State private var internalHighlightedNodeID: Int? = nil
    @Binding var hasStartedLineDrag: Bool

    
    var body: some View {
        let objectPosition = coordinateSystem.coordinateToPoint(x: object.position.x, y: object.position.y)
        let pixelSize = coordinateSystem.sizeInPixels(mmSize: object.size)

        let nodeDragGesture = DragGesture()
            .onChanged { value in
                if viewModel.draggingNodeID == nil {
                    viewModel.onNodeDragStart(object)
                }
                viewModel.onNodeDragChanged(value.translation, coordinateSystem: coordinateSystem, node: object, onUpdateNodePosition: onUpdateNodePosition)
            }
            .onEnded { _ in
                viewModel.onNodeDragEnd(object, onUpdateNodePosition: onUpdateNodePosition)
            }

        let isHighlighted = (highlightedNodeID == object.databaseID && isDraggingConnection)

        // 通过 nodeTypes 找到当前节点的类型记录，然后根据名称判断是否是"富文本节点"
        let currentType = nodeTypes.first(where: { $0.id == object.nodeTypeID })

        // -------------------------------------------------
        // 下面这段是重点改动：区分富文本节点和其它节点
        // -------------------------------------------------
        let baseNodeView: AnyView
        if let currentType = currentType {
            if currentType.name == "富文本节点" {
                // 富文本节点 => 黄色
                baseNodeView = AnyView(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.yellow.opacity(0.8))
                        .frame(width: pixelSize.width, height: pixelSize.height)
                )
            }
            else if let colorHex = currentType.colorHex,
                    let c = Color(hex: colorHex) {
                // 用后端 color_hex
                baseNodeView = AnyView(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(c)
                        .frame(width: pixelSize.width, height: pixelSize.height)
                )
            }
            else {
                // colorHex 为空？就给个灰底
                baseNodeView = AnyView(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: pixelSize.width, height: pixelSize.height)
                )
            }
        } else {
            // nodeTypes 里没找到 => fallback
            baseNodeView = AnyView(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.2))
                    .frame(width: pixelSize.width, height: pixelSize.height)
            )
        }

        // 节点被高亮时的动画与边框
        let highlightedNodeView = baseNodeView
            .scaleEffect(isHighlighted ? 1.05 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isHighlighted)
            .overlay(
                isHighlighted ?
                    AnyView(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.orange, lineWidth: 3)
                            .shadow(color: Color.orange.opacity(0.8), radius: 8, x: 0, y: 0)
                    )
                    : AnyView(EmptyView())
            )

        return ZStack {
            // 1) 基础节点外观
            highlightedNodeView

            // 2) 节点里的文字
            Text(object.name ?? "无名节点")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.black)
                .frame(width: pixelSize.width - 10, height: pixelSize.height - 10, alignment: .center)
                .lineLimit(1)
                .truncationMode(.tail)

            // 3) 若当前节点是选中的且未在连线中，则显示锚点
            if selectedNodeID == object.databaseID && !isDraggingConnection {
                NodeAnchorsView(
                    objectPosition: objectPosition,
                    pixelSize: pixelSize,
                    anchorSize: anchorSize,
                    anchorOffset: anchorOffset,
                    isDraggingConnection: $isDraggingConnection,
                    connectionStartNodeID: $connectionStartNodeID,
                    connectionStartPos: $connectionStartPos,
                    connectionCurrentMousePos: $connectionCurrentMousePos,
                    highlightedNodeID: highlightedNodeID,
                    onCreateConnection: onCreateConnection,
                    endConnectionDrag: endConnectionDrag,
                    beginConnectionDrag: beginConnectionDrag(fromNode:anchorPos:),
                    object: object,
                    hasStartedLineDrag: $hasStartedLineDrag,

                    // ★ 新增的两个闭包
                    doesConnectionExist: { fromID, toID in
                        // 这里写"判断这两个节点之间是否已有连线"的逻辑
                        // 通常是查你的 viewModel.connections 数组
                        // 例如:
                        viewModel.connections.contains { conn in
                            (conn.fromNodeID == fromID && conn.toNodeID == toID)
                            || (conn.fromNodeID == toID && conn.toNodeID == fromID)
                        }
                    },
                    onDeleteConnection: { fromID, toID in
                        if let existingConn = viewModel.connections.first(where: {
                            ($0.fromNodeID == fromID && $0.toNodeID == toID)
                            || ($0.fromNodeID == toID && $0.toNodeID == fromID)
                        }) {
                            // 调用刚才补充的函数
                            viewModel.deleteConnectionInBackend(existingConn.id) { success in
                                if success {
                                    // 如果需要，UI 里再做一些后续处理
                                    DispatchQueue.main.async {
                                        // 强制刷新画面
                                        viewModel.redrawToken = UUID()
                                    }
                                }
                            }
                        }
                    }
                )
            }


        }
        // ★ 这行是关键：macOS 下 SwiftUI 会在鼠标悬停时自动显示 tooltip
        .help(object.name ?? "无名节点的完整名字")
        
        .position(x: objectPosition.x, y: objectPosition.y)
        .gesture(isDraggingConnection ? nil : nodeDragGesture)
        .onTapGesture {
            onNodeClicked(object)
            // 如果是富文本节点，点击后打开编辑器
            if currentType?.name == "富文本节点", let dbID = object.databaseID {
                selectedRichTextNodeID.wrappedValue = dbID
            }
        }
        // 右键菜单
        .contextMenu {
            Button(role: .destructive) {
                onDeleteNode(object)
            } label: {
                Label("删除节点", systemImage: "trash")
            }
        }

    }

    

    func beginConnectionDrag(fromNode node: GraphicObject, anchorPos: CGPoint) {
        guard let startID = node.databaseID else { return }
        isDraggingConnection = true
        connectionStartNodeID = startID
        connectionStartPos = anchorPos
        connectionCurrentMousePos = anchorPos
        hasStartedLineDrag = false
    }
}



// NodeSystemView.swift (更新后的代码)
// NodeSystemView.swift (更新后的代码)
struct NodeSystemView: View {
    @ObservedObject var viewModel: NodeSystemViewModel
    let coordinateSystem: CoordinateSystem
    let nodeTypes: [NodeTypeItem]
    @Binding var selectedRichTextNodeID: Int?

    let onCreateNode: (CGFloat, CGFloat, String, Int, Double, Double, @escaping ()->Void) -> Void
    let onDeleteNode: (GraphicObject) -> Void
    let onUpdateNodePosition: (Int, CGPoint, CGSize) -> Void
    let onCreateConnection: (Int, Int) -> Void
    let shouldDrawConnections: Bool

    @State private var isDraggingConnection = false
    @State private var connectionStartNodeID: Int? = nil
    @State private var connectionStartPos: CGPoint = .zero
    @State private var connectionCurrentMousePos: CGPoint = .zero
    @State private var highlightedNodeID: Int? = nil

    let anchorSize: CGFloat = 20
    let anchorOffset: CGFloat = 5

    @State private var hasStartedLineDrag = false
    
    // 围栏相关（已去除动画相关的 state 和定时器）
    @State private var clusters: [ClusterInfo] = []

    var body: some View {
        ZStack {
            NodeConnectionLinesView(
                viewModel: viewModel,
                coordinateSystem: coordinateSystem,
                shouldDrawConnections: shouldDrawConnections
            )
            .id(viewModel.redrawToken)
            .id(viewModel.objects.map(\.id))
            .id(viewModel.connections.map(\.id))
            .allowsHitTesting(false)

            ForEach(viewModel.objects) { object in
                NodeItemView(
                    object: object,
                    coordinateSystem: coordinateSystem,
                    selectedNodeID: viewModel.selectedNodeID,
                    anchorSize: anchorSize,
                    anchorOffset: anchorOffset,
                    isDraggingConnection: $isDraggingConnection,
                    connectionStartNodeID: $connectionStartNodeID,
                    connectionStartPos: $connectionStartPos,
                    onNodeClicked: { obj in viewModel.onNodeClicked(obj) },
                    onDeleteNode: onDeleteNode,
                    onUpdateNodePosition: onUpdateNodePosition,
                    selectedRichTextNodeID: $selectedRichTextNodeID,
                    viewModel: viewModel,
                    
                    //NodeItemView 新增了一个 nodeTypes: [NodeTypeItem] 参数，所以在调用它时也必须传入这个参数。
                    // 这里就是你需要补上的
                    nodeTypes: nodeTypes,
                    
                    highlightedNodeID: $highlightedNodeID,
                    connectionCurrentMousePos: $connectionCurrentMousePos,
                    onCreateConnection: onCreateConnection,
                    endConnectionDrag: endConnectionDrag,
                    hasStartedLineDrag: $hasStartedLineDrag
                )
            }

            // 使用虚线圆围栏，不再使用动画，不需要phase
            ClusterFencesView(
                clusters: clusters,
                coordinateSystem: coordinateSystem
            )
            .allowsHitTesting(false)

            if isDraggingConnection && hasStartedLineDrag {
                Path { path in
                    path.move(to: connectionStartPos)
                    path.addLine(to: connectionCurrentMousePos)
                }
                .stroke(style: StrokeStyle(lineWidth: 3, dash: [8,4]))
                .foregroundColor(Color.blue.opacity(0.7))
                .allowsHitTesting(false)
            }
        }
        .gesture(
            DragGesture(minimumDistance: 0)
                .onChanged { value in
                    if isDraggingConnection {
                        connectionCurrentMousePos = value.location
                        highlightNodeUnderMouse(at: value.location)
                    }
                }
                .onEnded { value in
                    if isDraggingConnection {
                        connectionCurrentMousePos = value.location
                        if let targetNodeID = highlightedNodeID, let startID = connectionStartNodeID {
                            onCreateConnection(startID, targetNodeID)
                        }
                        endConnectionDrag()
                    }
                }
        )
        .onChange(of: viewModel.objects) { newValue in
            // 若节点从无到有，就画一次围栏
            // 或者只在 newValue 不再变化时延迟0.3秒再 BFS 等等
            if newValue.count > 0 {
                updateClusters()
            }
        }
        .onChange(of: viewModel.redrawToken) { _ in
            updateClusters()
        }
        .onAppear {
            updateClusters()
        }
    }

    func endConnectionDrag() {
        isDraggingConnection = false
        connectionStartNodeID = nil
        highlightedNodeID = nil
        hasStartedLineDrag = false
    }

    func highlightNodeUnderMouse(at mousePos: CGPoint) {
        if let node = viewModel.findNodeAtPosition(mousePos, coordinateSystem: coordinateSystem),
           let nodeID = node.databaseID {
            highlightedNodeID = nodeID
        } else {
            highlightedNodeID = nil
        }
    }

    private func updateClusters() {
        let nodes = viewModel.objects
        guard !nodes.isEmpty else {
            clusters = []
            return
        }

        // 根据节点的最大高度确定初步距离阈值
        let maxHeight = nodes.map { $0.size.height }.max() ?? 50
        
        //以下代码可以调节围栏的触发精度，通过调整以下数据可以实现。
        let threshold = maxHeight * 5.0

        var visited = Set<UUID>()
        var result: [ClusterInfo] = []

        for node in nodes {
            if visited.contains(node.id) { continue }

            // BFS/DFS 寻找簇
            var clusterNodes: [GraphicObject] = []
            var queue = [node]

            while !queue.isEmpty {
                let current = queue.removeFirst()
                if visited.contains(current.id) { continue }
                visited.insert(current.id)
                clusterNodes.append(current)

                for neighbor in nodes where neighbor.id != current.id && !visited.contains(neighbor.id) {
                    let dx = neighbor.position.x - current.position.x
                    let dy = neighbor.position.y - current.position.y
                    let dist = sqrt(dx*dx + dy*dy)
                    if dist < threshold {
                        queue.append(neighbor)
                    }
                }
            }

            // 计算圆心和半径
            if clusterNodes.count > 1 {
                let cx = clusterNodes.map { $0.position.x }.reduce(0, +) / CGFloat(clusterNodes.count)
                let cy = clusterNodes.map { $0.position.y }.reduce(0, +) / CGFloat(clusterNodes.count)
                let center = CGPoint(x: cx, y: cy)
                
                // 找出最远距离与最大对角线
                var maxDist: CGFloat = 0.0
                var maxDiagonal: CGFloat = 0.0
                for n in clusterNodes {
                    let dx = n.position.x - cx
                    let dy = n.position.y - cy
                    let dist = sqrt(dx*dx + dy*dy)
                    maxDist = max(maxDist, dist)

                    let w = n.size.width
                    let h = n.size.height
                    let diag = sqrt(w*w + h*h)
                    maxDiagonal = max(maxDiagonal, diag)
                }
                // 在最远节点距离基础上，加上最大对角线的一半，并略微放大
                let radius = maxDist + (maxDiagonal / 2.0) * 1.2
                result.append(ClusterInfo(center: center, radius: radius))
            }
        }

        clusters = result
    }
    
    
    //围栏相关的代码
    struct ClusterInfo {
        let center: CGPoint
        let radius: CGFloat
    }
}


//可以让围栏的虚线看起来像是沿着圆周缓慢转动：利用 StrokeStyle 中的 dashPhase 属性。通过定期改变 dashPhase 的值，虚线的断续段看上去会沿着圆的周长滚动，从而产生动态旋转的视觉效果。
struct ClusterFencesView: View {
    let clusters: [NodeSystemView.ClusterInfo]
    let coordinateSystem: CoordinateSystem

    @State private var dashPhase: CGFloat = 0.0
    //若围栏的动画造成程序比较卡，可以调节下面的时钟间隔，但是一般情况下这个间隔不会影响系统，影响系统反应的主要是不能在body中大量绘制虚线，直线
    @State private var timer = Timer.publish(every: 0.05, on: .main, in: .common).autoconnect()

    var body: some View {
        Canvas { context, size in
            // 对每个簇绘制虚线圆围栏，并利用 dashPhase 达到旋转效果
            for cluster in clusters {
                drawFence(for: cluster, in: context, size: size, dashPhase: dashPhase)
            }
        }
        .onReceive(timer) { _ in
            // 每隔0.05秒更新一次dashPhase
            // 假设dash数组为[8,4]，总长度12
            // 当dashPhase超过12后重置为0，这样循环移动
            dashPhase += 0.5
            if dashPhase > 12 {
                dashPhase = 0
            }
        }
    }

    func drawFence(for cluster: NodeSystemView.ClusterInfo, in context: GraphicsContext, size: CGSize, dashPhase: CGFloat) {
        // 将逻辑坐标中心和半径转为屏幕坐标
        let centerScreen = coordinateSystem.coordinateToPoint(x: cluster.center.x, y: cluster.center.y)
        let radiusPixels = cluster.radius * (coordinateSystem.spacingInPixels / coordinateSystem.spacingInMM)

        var path = Path()
        let rect = CGRect(x: centerScreen.x - radiusPixels, y: centerScreen.y - radiusPixels, width: radiusPixels * 2, height: radiusPixels * 2)
        path.addEllipse(in: rect)

        // 使用虚线风格的圆形围栏，并加入dashPhase
        context.stroke(
            path,
            with: .color(Color.blue.opacity(0.7)),
            style: StrokeStyle(
                lineWidth: 3,
                lineCap: .round,
                
                //以下代码调节虚线的密疏，可以改成 10,5，或者其它的，自己调
                dash: [8,4],     // 实线8，空白4
                dashPhase: dashPhase
            )
        )
    }
}

// =============== 新增: 系统选择与新建系统功能 ===============

struct CreateSystemPanelView: View {
    @Binding var systemName: String
    @Binding var systemDescription: String
    var onCreate: ()->Void
    var onCancel: ()->Void

    var body: some View {
        VStack(spacing: 20) {
            Text("新建系统（任务系统）")
                .font(.title)
                .fontWeight(.bold)
                .padding(.top, 20)

            TextField("系统名称", text: $systemName)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .padding([.leading, .trailing], 20)

            TextField("系统描述", text: $systemDescription)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .padding([.leading, .trailing], 20)

            HStack(spacing: 30) {
                Button("创建") {
                    onCreate()
                }
                .buttonStyle(.borderedProminent)
                .tint(.blue)
                .font(.system(size: 16, weight: .bold))

                Button("取消") {
                    onCancel()
                }
                .buttonStyle(.bordered)
                .font(.system(size: 16))
            }
            .padding(.bottom, 20)
        }
        .frame(width: 300)
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(Color.white.opacity(0.9))
                .shadow(radius: 10)
        )
        .padding()
    }
}




struct SearchBar: View {
    @Binding var text: String
    
    var body: some View {
        HStack {
            TextField("搜索系统...", text: $text)
                .textFieldStyle(PlainTextFieldStyle())
                .padding(10)
                .padding(.horizontal, 25)
                .background(Color(.controlBackgroundColor))
                .cornerRadius(8)
                .overlay(
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.gray)
                            .frame(minWidth: 0, maxWidth: .infinity, alignment: .leading)
                            .padding(.leading, 8)
                        
                        if !text.isEmpty {
                            Button(action: { self.text = "" }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.gray)
                                    .padding(.trailing, 8)
                            }
                        }
                    }
                )
                // 添加微妙的边框
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                )
        }
    }
}


/// 自定义删除确认提示组件，不使用系统内置的 .alert/.sheet 等
struct CustomDeleteConfirmation: View {
    var message: String
    var onConfirm: () -> Void
    var onCancel: () -> Void

    var body: some View {
        ZStack {
            // 半透明背景覆盖全屏
            Color.black.opacity(0.5)
                .edgesIgnoringSafeArea(.all)
            // 自定义对话框
            VStack(spacing: 20) {
                Text("确认删除")
                    .font(.headline)
                    .foregroundColor(.black)
                Text(message)
                    .font(.body)
                    .foregroundColor(.black)
                    .multilineTextAlignment(.center)
                HStack(spacing: 30) {
                    Button("删除") {
                        onConfirm()
                    }
                    .padding()
                    .background(Color.red)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                    
                    Button("取消") {
                        onCancel()
                    }
                    .padding()
                    .background(Color.gray)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            }
            .padding()
            .background(Color.white)
            .cornerRadius(12)
            .shadow(radius: 10)
            .frame(maxWidth: 300)
        }
    }
}


/// 系统模型（任务模型）保持不变
/// 后端 /systems 路由现在返回的 JSON 结构：
/// [
///   {
///     "id": Int,
///     "name": String,
///     "description": String or null,
///     "created_at": String,
///     "updated_at": String
///   },
///   ...
/// ]
struct MySystemItem: Identifiable, Codable {
    let id: Int
    let name: String
    let description: String?
    let created_at: String
    let updated_at: String
}

class SystemViewModel: ObservableObject {
    @Published var systems: [MySystemItem] = []
    let baseURL: String
    let authKey: String
    
    init(baseURL: String, authKey: String) {
        self.baseURL = baseURL
        self.authKey = authKey
    }

    func fetchSystems() {
        guard let url = URL(string: NetworkConfig.monitorServerForD3test + NetworkConfig.Endpoints.systems) else { return }
        var request = URLRequest(url: url)
        request.setValue(authKey, forHTTPHeaderField: "Authorization")
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let data = data {
                do {
                    let items = try JSONDecoder().decode([MySystemItem].self, from: data)
                    DispatchQueue.main.async {
                        self.systems = items
                    }
                } catch {
                    print("JSON解析失败：", error)
                }
            }
        }.resume()
    }
}

/// 把 fullText 中第一次出现的 searchText 高亮显示
func highlightSearchResult(_ fullText: String, searchText: String) -> Text {
    // 如果关键字为空，或者全文里找不到匹配，就直接返回普通 Text
    guard !searchText.isEmpty,
          let range = fullText.range(of: searchText, options: .caseInsensitive) else {
        return Text(fullText)
    }
    
    // 找到匹配范围后，拆成三段：匹配前、匹配中、匹配后
    let beforeMatch = String(fullText[..<range.lowerBound])
    let match = String(fullText[range])
    let afterMatch = String(fullText[range.upperBound...])
    
    // 拼接成一个 Text，给 match 部分特殊样式
    return Text(beforeMatch)
        + Text(match)
            .foregroundColor(.yellow) // 或者 .bold()、加背景等
        + Text(afterMatch)
}


struct TruncatedTextField: NSViewRepresentable {
    let fullText: String
    let width: Double
    let font: NSFont
    
    func makeNSView(context: Context) -> NSTextField {
        let textField = NSTextField()
        textField.isBezeled = false
        textField.isSelectable = false
        textField.isEnabled = false
        textField.font = font
        textField.toolTip = fullText
        textField.stringValue = truncateText(fullText, width: width, font: font)
        return textField
    }
    
    func updateNSView(_ nsView: NSTextField, context: Context) {
        nsView.stringValue = truncateText(fullText, width: width, font: font)
        nsView.toolTip = fullText
    }
    
    private func truncateText(_ text: String, width: Double, font: NSFont) -> String {
        let attributedString = NSAttributedString(string: text, attributes: [.font: font])
        let textSize = attributedString.size()
        if textSize.width <= width {
            return text
        }
        var start = 0
        var end = text.count - 1
        while start < end {
            let mid = (start + end + 1) / 2
            let substring = String(text.prefix(mid))
            let attributedSubstring = NSAttributedString(string: substring, attributes: [.font: font])
            let subSize = attributedSubstring.size()
            if subSize.width <= width {
                start = mid
            } else {
                end = mid - 1
            }
        }
        let truncatedText = String(text.prefix(start + 1)) + "…"
        return truncatedText
    }
}

/// 新设计的系统卡片
// 先多加一个参数 searchText，让这个卡片知道用户正在搜索什么
struct SystemCardViewEnhanced: View {
    let system: MySystemItem
    let isSelected: Bool
    let searchText: String
    var onDelete: () -> Void

    var body: some View {
        ZStack(alignment: .topTrailing) {
            VStack(alignment: .leading, spacing: 10) {
                // 将系统名称显示为深色文字
                highlightSearchResult(system.name, searchText: searchText)
                    .font(.title2.bold())
                    .foregroundColor(.black)
                
                if let desc = system.description, !desc.isEmpty {
                    highlightSearchResult(desc, searchText: searchText)
                        .font(.body)
                        .foregroundColor(.black.opacity(0.8))
                        .lineLimit(3)
                }
                
                if !system.created_at.isEmpty {
                    Text("创建于 \(system.created_at)")
                        .font(.caption)
                        .foregroundColor(.black.opacity(0.7))
                }
            }
            .padding(16)
            .padding(.top, 24)
            .padding(.trailing, 24)
            .background(isSelected ? Color.blue.opacity(0.3) : Color.white.opacity(0.9))
            .cornerRadius(15)
            .overlay(
                RoundedRectangle(cornerRadius: 15)
                    .stroke(isSelected ? Color.orange : Color.clear, lineWidth: 4)
            )
            .shadow(radius: 5)
            
            Button(action: onDelete) {
                Image(systemName: "trash")
                    .foregroundColor(.red)
                    .padding(8)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
}

/// 全新设计的任务选择界面
struct EnhancedSelectSystemView: View {
    @ObservedObject var viewModel: SystemViewModel
    @State private var searchText: String = ""
    @Binding var selectedSystemID: Int?
    var onSelect: () -> Void
    var onCreateNewSystem: () -> Void
    var onDeleteSystem: (Int) -> Void

    // 新增状态：待删除的系统
    @State private var systemToDelete: MySystemItem? = nil

    // 根据搜索条件过滤任务
    var filteredSystems: [MySystemItem] {
        if searchText.isEmpty {
            return viewModel.systems
        }
        return viewModel.systems.filter {
            $0.name.localizedCaseInsensitiveContains(searchText) ||
            ($0.description?.localizedCaseInsensitiveContains(searchText) ?? false)
        }
    }

    // 原来用于布局的列
    let columns = [GridItem(.adaptive(minimum: 250), spacing: 16)]
    
    var body: some View {
        ZStack {
            // 背景渐变
            LinearGradient(gradient: Gradient(colors: [
                Color(red: 1.0, green: 0.95, blue: 0.8),
                Color(red: 1.0, green: 0.9, blue: 0.7)
            ]), startPoint: .topLeading, endPoint: .bottomTrailing)
            .edgesIgnoringSafeArea(.all)
            
            // 用一个水平布局 + Spacer，把主要内容挤到中间
            HStack {
                Spacer(minLength: 0)  // 左侧空白
                VStack(spacing: 20) {
                    
                    Text("选择任务系统")
                        .font(.largeTitle.bold())
                        .foregroundColor(.white)
                        .padding(.top, 50)
                    
                    // 搜索栏
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.white)
                        TextField("搜索任务...", text: $searchText)
                            .textFieldStyle(PlainTextFieldStyle())
                            .padding(10)
                            .background(Color.white.opacity(0.3))
                            .cornerRadius(8)
                    }
                    .padding(.horizontal, 40)
                    
                    // 滚动区域 + 网格
                    ScrollView {
                        LazyVGrid(columns: columns, spacing: 20) {
                            ForEach(filteredSystems) { system in
                                SystemCardViewEnhanced(
                                    system: system,
                                    isSelected: selectedSystemID == system.id,
                                    searchText: searchText,
                                    onDelete: {
                                        // 点击删除按钮时，将待删除任务记录下来
                                        systemToDelete = system
                                    }
                                )
                                .onTapGesture {
                                    selectedSystemID = system.id
                                }
                            }
                        }
                        .padding(.horizontal, 40)
                        .padding(.top, 20)
                        .safeAreaInset(edge: .top) {
                            Color.clear.frame(height: 20)
                        }
                    }
                    .padding(.top, 10)
                    
                    // 底部按钮
                    HStack(spacing: 30) {
                        Button(action: onCreateNewSystem) {
                            Text("新建系统")
                                .font(.headline)
                                .padding()
                                .frame(maxWidth: .infinity)
                        }
                        .background(
                            LinearGradient(gradient: Gradient(colors: [Color.green, Color.blue]),
                                           startPoint: .leading,
                                           endPoint: .trailing)
                        )
                        .foregroundColor(.white)
                        .cornerRadius(10)
                        
                        Button(action: {
                            if selectedSystemID != nil {
                                onSelect()
                            }
                        }) {
                            Text("进入系统")
                                .font(.headline)
                                .padding()
                                .frame(maxWidth: .infinity)
                        }
                        .background(
                            LinearGradient(gradient: Gradient(colors: [Color.orange, Color.red]),
                                           startPoint: .leading,
                                           endPoint: .trailing)
                        )
                        .foregroundColor(.white)
                        .cornerRadius(10)
                        .disabled(selectedSystemID == nil)
                    }
                    .padding(.horizontal, 40)
                    .padding(.bottom, 40)
                    
                }
                .frame(maxWidth: 1000)   // <-- 关键：限制最大宽度，比如1000
                .padding(.vertical, 20)  // 让上下也留点余白
                Spacer(minLength: 0)     // 右侧空白
            }
            
            // 如果有要删除的系统，弹出自定义确认
            if let system = systemToDelete {
                CustomDeleteConfirmation(
                    message: "确定要删除任务 '\(system.name)' 吗？此操作无法撤销。",
                    onConfirm: {
                        onDeleteSystem(system.id)
                        systemToDelete = nil
                    },
                    onCancel: {
                        systemToDelete = nil
                    }
                )
            }
        }
        .onAppear {
            viewModel.fetchSystems()
        }
    }
}

struct ActionButtonBar: View {
    let onCreate: () -> Void
    let onConfirm: () -> Void
    @Binding var selectedSystemID: Int?
    
    var body: some View {
        HStack(spacing: 16) {
            Button(action: onCreate) {
                Label("新建系统", systemImage: "plus")
                    .frame(minWidth: 120)
            }
            .buttonStyle(GradientButtonStyle(gradient: .blueGradient))
            
            Spacer()
            
            Button(action: onConfirm) {
                Label("进入系统", systemImage: "arrow.right.circle")
                    .frame(minWidth: 120)
            }
            .buttonStyle(GradientButtonStyle(gradient: .greenGradient))
            .disabled(selectedSystemID == nil)
        }
    }
}

// 渐变色按钮样式
struct GradientButtonStyle: ButtonStyle {
    let gradient: LinearGradient
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.subheadline.bold())
            .foregroundColor(.white)
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(gradient)
                    .shadow(color: .black.opacity(0.2), radius: 3, x: 0, y: 2)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeOut(duration: 0.2), value: configuration.isPressed)
    }
}

extension LinearGradient {
    // 定义一个默认颜色，当 Color(hex:) 返回 nil 时使用
    private static let defaultColor = Color.gray
    
    static let blueGradient = LinearGradient(
        // 使用空合运算符为每个颜色提供默认值
        colors: [Color(hex: "#4A90E2") ?? defaultColor, Color(hex: "#1873D3") ?? defaultColor],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    static let greenGradient = LinearGradient(
        // 使用空合运算符为每个颜色提供默认值
        colors: [Color(hex: "#32CD32") ?? defaultColor, Color(hex: "#228B22") ?? defaultColor],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
}

// 新增的交互管理器，用于处理富文本节点外观、名称显示限制以及拉线操作
struct NodeInteractionManager {
    let richTextNodeTypeID = 9

    // 拉线相关状态（这里仅定义属性和方法示意，需要在实际使用中结合鼠标事件更新它们）
    var isDraggingConnection: Bool = false
    var connectionStartNodeID: Int? = nil
    var connectionCurrentMousePos: CGPoint = .zero
    var highlightedNodeID: Int? = nil

    func nodeStyle(for nodeTypeID: Int) -> some View {
        if nodeTypeID == richTextNodeTypeID {
            // 富文本节点：使用浅黄色背景 + 棕色描边，类似便签
            return AnyView(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.yellow.opacity(0.8))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.brown, lineWidth: 1)
                    )
            )
        } else {
            // 普通节点：维持原有渐变风格
            return AnyView(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(gradient: Gradient(colors: [Color.blue, Color.purple]),
                                       startPoint: .topLeading,
                                       endPoint: .bottomTrailing)
                    )
            )
        }
    }

    func nodeNameView(_ name: String?, width: CGFloat, height: CGFloat) -> some View {
        Text(name ?? "无名节点")
            .font(.system(size: 14, weight: .bold))
            .foregroundColor(.black)
            .frame(width: width - 10, height: height - 10, alignment: .center)
            .lineLimit(1)
            .truncationMode(.tail)
    }

    // 返回用于拉线起点的锚点位置（举例：节点左右两侧）
    func anchorPointsAroundNode(position: CGPoint, size: CGSize) -> [CGPoint] {
        let halfW = size.width / 2
        let halfH = size.height / 2
        // 简单示例：左右两侧各一个锚点
        return [
            CGPoint(x: position.x + halfW + 5, y: position.y),
            CGPoint(x: position.x - halfW - 5, y: position.y)
        ]
    }
}

extension Color {
    /// 例如输入 "#FF00AA" 或 "#ff00aa"
    init?(hex: String) {
        var hexString = hex.trimmingCharacters(in: .whitespacesAndNewlines)
                         .uppercased()
        if hexString.hasPrefix("#") {
            hexString.removeFirst()
        }
        // 必须是6位RGB
        guard hexString.count == 6 else {
            return nil
        }
        var rgbValue: UInt64 = 0
        Scanner(string: hexString).scanHexInt64(&rgbValue)

        let r = Double((rgbValue & 0xFF0000) >> 16) / 255.0
        let g = Double((rgbValue & 0x00FF00) >> 8) / 255.0
        let b = Double(rgbValue & 0x0000FF) / 255.0

        self = Color(red: r, green: g, blue: b)
    }
}


// ======================== D3test主视图 ===========================

struct D3test: View {
    // 添加返回回调参数和系统ID参数
    let onReturn: (() -> Void)?
    let systemID: Int?

    // 初始化器
    init(onReturn: (() -> Void)? = nil, systemID: Int? = nil) {
        self.onReturn = onReturn
        self.systemID = systemID
        // 如果提供了systemID，直接进入任务系统；否则显示系统选择页面
        _showSystemSelection = State(initialValue: systemID == nil)
        if let sysID = systemID {
            _selectedSystemID = State(initialValue: sysID)
        }
    }
    
    @State private var zoomScale: CGFloat = 1.0
    @State private var offsetX: CGFloat = 0.0
    @State private var offsetY: CGFloat = 0.0
    @State private var dragOffset = CGSize.zero
    @State private var showCorePoints: Bool = false
    
    @State private var lineSpacingMM: Double = 50.0
    @State private var showDashedLines: Bool = true
    @State private var dashedLineOpacity: Double = 0.3
    @State private var showCoordinateLabels: Bool = true
    @State private var coordinateLabelOpacity: Double = 0.8
    
    @State private var isIconTapped: Bool = false
    @State private var isDragging: Bool = false
    
    @State private var showingAddNodePanel = false
    @State private var newNodeName: String = ""
    @State private var newNodeTypeID: Int = 1
    @State private var newNodeWidthMM: Double = 50
    @State private var newNodeHeightMM: Double = 50
    
    @State private var nodeTypes: [NodeTypeItem] = []
    
    @State private var selectedNodeID: Int? = nil
    
    @State private var isDraggingConnection = false
    @State private var connectionDragStartNodeID: Int? = nil
    @State private var connectionDragStartPos: CGPoint? = nil
    @State private var connectionDragEndPos: CGPoint? = nil
    
    @State private var showingNodeCreationPanelAfterDrag = false
    @State private var pendingConnectionFromNodeID: Int? = nil
    @State private var pendingConnectionPosition: CGPoint? = nil
    
    @State private var richTextEditorWidth: CGFloat = 900
    @State private var richTextEditorHeight: CGFloat = 900
    
    let baseURL = NetworkConfig.monitorServerForD3test
    let authKey = "13795375098"
    
    @State private var fenceAnimationPhase: CGFloat = 0.0
    @State private var fenceTimer = Timer.publish(every: 0.05, on: .main, in: .common).autoconnect()

    @State private var viewControlParamsID: Int? = nil
    @State private var showDeleteConfirmation: Bool = false
    @State private var nodeToDelete: GraphicObject? = nil

    @State private var selectedRichTextNodeID: Int? = nil
    @State private var richTextEditorCumulativeOffset: CGSize = .zero
    
    @State private var debugPrintOnce = false
    
    @State private var isLoading: Bool = false
    @State private var loadError: String? = nil
    
    @State private var colorPhase: Double = 0.0
    @State private var timer: Timer? = nil
    
    @State private var connectionControlPoints: [Int: CGPoint] = [:]
    
    @State private var shouldDrawConnections = true

    // 新增字段：系统相关
    @State private var systems: [MySystemItem] = []
    @State private var selectedSystemID: Int? = nil
    @State private var showSystemSelection = true
    @State private var showingCreateSystemPanel = false
    @State private var newSystemName = ""
    @State private var newSystemDescription = ""

    @StateObject private var nodeSystemViewModelHolder = ObservableObjectHolder<NodeSystemViewModel>()
    
    @StateObject private var systemVM = SystemViewModel(
        baseURL: NetworkConfig.monitorServerForD3test,
        authKey: "13795375098"
    )
    
    var nodeSystemViewModel: NodeSystemViewModel {
        if let vm = nodeSystemViewModelHolder.value {
            return vm
        } else {
            let vm = NodeSystemViewModel(
                baseURL: NetworkConfig.monitorServerForD3test,
                authKey: authKey,
                systemID: selectedSystemID ?? 1
            )
            nodeSystemViewModelHolder.value = vm
            return vm
        }
    }

    
    // =========================================
    // ===============   BODY   ===============
    // =========================================
    var body: some View {
        GeometryReader { geometry in
            if showSystemSelection {
                // 拆解：原先 if showSystemSelection { ... } 大段搬到一个方法
                systemSelectionOverlay(geometry: geometry)
            } else {
                // 拆解：原先 else { ... } 的大段搬到另一个方法
                mainScreenContent(geometry: geometry)
            }
        }
    }
    
    
    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        // ~~~~~~~~~~~~~  以下是从 body 中拆出来的两个私有方法 ~~~~~~~~~~~~~~
        // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

        /// 显示「系统选择 / 新建系统」的弹窗逻辑
        private func systemSelectionOverlay(geometry: GeometryProxy) -> some View {
            // === 原先的内容 ===
            ZStack {
                Color.black.opacity(0.5).ignoresSafeArea()
                if showingCreateSystemPanel {
                    CreateSystemPanelView(systemName: $newSystemName, systemDescription: $newSystemDescription, onCreate: {
                        createSystem(name: newSystemName, description: newSystemDescription) {
                            newSystemName = ""
                            newSystemDescription = ""
                            loadSystems {
                                showingCreateSystemPanel = false
                            }
                        }
                    }, onCancel: {
                        showingCreateSystemPanel = false
                    })
                } else {
                    EnhancedSelectSystemView(viewModel: systemVM, selectedSystemID: $selectedSystemID, onSelect: {
                        if let sid = selectedSystemID {
                            showSystemSelection = false
                            // 选定 system 后重新加载数据
                            performFullLoad(systemID: sid)
                        }
                    }, onCreateNewSystem: {
                        showingCreateSystemPanel = true
                    },
                                     onDeleteSystem: { sid in
                                         // 在这里调你的删除函数
                                         // e.g. self.deleteSystem(sid) { success, errMsg in ... }
                                         deleteSystem(sid) { success, errorMsg in
                                             if success {
                                                 // refresh
                                                 loadSystems {}
                                             } else {
                                                 // show error
                                             }
                                         }
                                     }
                    
                    
                    
                    )
                    .onAppear {
                        loadSystems {}
                    }
                }
            }
        }

        /// 主画面内容：背景、节点系统、控制面板、富文本编辑、各种 Overlay
        private func mainScreenContent(geometry: GeometryProxy) -> some View {
            // === 原先的 else { ... } 整段 code 都放这里 ===

            let width = geometry.size.width
            let height = geometry.size.height
            let centerX = width / 2
            let centerY = height / 2
            
            let pixelsPerMM = self.calculatePixelsPerMM(geometry: geometry)
            let spacingInPixels = CGFloat(lineSpacingMM) * pixelsPerMM
            let spacingInMM = CGFloat(lineSpacingMM) * zoomScale

            let coordinateSystem = self.makeCoordinateSystem(
                width: width,
                height: height,
                centerX: centerX,
                centerY: centerY,
                spacingInPixels: spacingInPixels,
                spacingInMM: spacingInMM,
                pixelsPerMM: pixelsPerMM
            )

            return ZStack(alignment: .topLeading) {
                
                // 1. 背景
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(red: 0.8, green: 1.0, blue: 0.8),
                                Color(red: 0.9, green: 1.0, blue: 0.85)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .contentShape(Rectangle())
                    .edgesIgnoringSafeArea(.all)
                    .onTapGesture {
                        selectedNodeID = nil
                    }
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                if selectedRichTextNodeID == nil {
                                    isDragging = true
                                    dragOffset = value.translation
                                }
                            }
                            .onEnded { value in
                                if selectedRichTextNodeID == nil {
                                    isDragging = false
                                    handleMainDragEnd(
                                        value: value,
                                        width: width,
                                        height: height,
                                        spacingInPixels: spacingInPixels,
                                        spacingInMM: spacingInMM
                                    )
                                }
                            }
                    )

                // 2. 网格
                if showDashedLines || showCoordinateLabels {
                    AnimatedGridView(
                        adjustedOffsetX: coordinateSystem.adjustedOffsetX,
                        adjustedOffsetY: coordinateSystem.adjustedOffsetY,
                        width: width,
                        height: height,
                        centerX: centerX,
                        centerY: centerY,
                        spacingInPixels: spacingInPixels,
                        spacingInMM: spacingInMM,
                        pixelsPerMM: pixelsPerMM,
                        zoomScale: zoomScale,
                        lineSpacingMM: lineSpacingMM,
                        showDashedLines: showDashedLines,
                        dashedLineOpacity: dashedLineOpacity,
                        showCoordinateLabels: showCoordinateLabels,
                        coordinateLabelOpacity: coordinateLabelOpacity
                    )
                }

                // 3. 节点系统
                NodeSystemView(
                    viewModel: nodeSystemViewModel,
                    coordinateSystem: coordinateSystem,
                    nodeTypes: nodeTypes,
                    selectedRichTextNodeID: $selectedRichTextNodeID,
                    onCreateNode: { x, y, name, typeID, w, h, comp in
                        self.nodeSystemViewModel.createNode(
                            x: x,
                            y: y,
                            name: name,
                            typeID: typeID,
                            width: w,
                            height: h
                        ) {
                            loadNodes { _, _ in
                                comp()
                            }
                        }
                    },
                    onDeleteNode: { node in
                        self.nodeSystemViewModel.deleteNode(node: node) {
                            // nothing
                        }
                    },
                    onUpdateNodePosition: { id, pos, size in
                        self.nodeSystemViewModel.updateNodePositionInBackend(id, pos, size)
                    },
                    onCreateConnection: { from, to in
                        self.nodeSystemViewModel.createConnection(from: from, to: to)
                    },
                    shouldDrawConnections: shouldDrawConnections
                )

                // 4. 右上角的提示
                VStack(alignment: .leading) {
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "info.circle.fill")
                            .resizable()
                            .frame(width: 20, height: 20)
                            .foregroundColor(.blue)
                            .scaleEffect(isIconTapped ? 1.2 : 1.0)
                            .animation(.easeInOut(duration: 0.2), value: isIconTapped)

                        Text("本程序的坐标系使用毫米为单位，竖线和横线间距为50毫米。")
                            .font(.system(size: 14))
                            .foregroundColor(.black)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                    .onTapGesture {
                        withAnimation(.easeInOut(duration: 0.1)) {
                            isIconTapped = true
                        }
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            withAnimation(.easeInOut(duration: 0.1)) {
                                isIconTapped = false
                            }
                            withAnimation {
                                showCorePoints = true
                            }
                        }
                    }
                }
                .padding(10)
                .background(Color.white.opacity(0.9))
                .cornerRadius(10)
                .shadow(radius: 5)
                .padding(.leading, 20)
                .padding(.top, 20)

                // 5. 左上角返回按钮（向下移动到80像素位置以避免重叠）
                Button("返回") {
                    onReturn?()
                }
                .buttonStyle(MonochromeButtonStyle(isSquare: false))
                .offset(x: 20, y: 80)
                
                // 6. 右上角新建节点按钮
                Button("新建节点") {
                    newNodeName = ""
                    newNodeTypeID = nodeTypes.first?.id ?? 1
                    newNodeWidthMM = 180
                    newNodeHeightMM = 150
                    showingAddNodePanel = true
                }
                .buttonStyle(DynamicColorButtonStyle(hue: $colorPhase))
                .onAppear {
                    timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
                        withAnimation(.linear(duration: 0.1)) {
                            colorPhase = (colorPhase + 0.01).truncatingRemainder(dividingBy: 1.0)
                        }
                    }
                }
                .onDisappear {
                    timer?.invalidate()
                }
                .offset(x: width - 220, y: 40)

                // 6. 缩放、返回中心、坐标系按钮
                VStack(spacing: 20) {
                    HStack(spacing: 20) {
                        Button(action: {
                            zoomScale *= 1.25
                            shouldDrawConnections = false
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                shouldDrawConnections = true
                            }
                        }) {
                            Image(systemName: "minus.magnifyingglass")
                                .resizable()
                                .frame(width: 24, height: 24)
                        }
                        .buttonStyle(MonochromeButtonStyle(isSquare: true))

                        Button(action: {
                            zoomScale /= 1.25
                            shouldDrawConnections = false
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                shouldDrawConnections = true
                            }
                        }) {
                            Image(systemName: "plus.magnifyingglass")
                                .resizable()
                                .frame(width: 24, height: 24)
                        }
                        .buttonStyle(MonochromeButtonStyle(isSquare: true))
                    }

                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.5)) {
                            offsetX = 0.0
                            offsetY = 0.0
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "arrow.up.left.and.down.right.magnifyingglass")
                                .resizable()
                                .frame(width: 24, height: 24)
                            Text("返回屏幕中心")
                                .font(.system(size: 16, weight: .medium))
                        }
                    }
                    .buttonStyle(MonochromeButtonStyle(isSquare: false))

                    if !showDashedLines && !showCoordinateLabels {
                        Button("坐标系") {
                            showDashedLines = true
                            showCoordinateLabels = true
                            updateViewControlParamsInBackend()
                        }
                        .buttonStyle(MonochromeButtonStyle(isSquare: false))
                    }
                }
                .background(Color.gray.opacity(0.3))
                .cornerRadius(10)
                .shadow(radius: 5)
                .padding(.trailing, 20)
                .padding(.top, 120)
                .frame(maxWidth: .infinity, alignment: .trailing)
                .overlay(
                    Group {
                        if showDashedLines || showCoordinateLabels {
                            ControlPanelView(
                                lineSpacingMM: $lineSpacingMM,
                                showDashedLines: $showDashedLines,
                                dashedLineOpacity: $dashedLineOpacity,
                                showCoordinateLabels: $showCoordinateLabels,
                                coordinateLabelOpacity: $coordinateLabelOpacity,
                                showCorePoints: $showCorePoints
                            )
                            .position(x: width / 2, y: height - 80)
                        }
                    }
                )

                // 7. 显示核心点信息
                if showCorePoints {
                    ZStack {
                        Color.black.opacity(0.3)
                            .edgesIgnoringSafeArea(.all)
                            .onTapGesture {
                                withAnimation {
                                    showCorePoints = false
                                }
                            }

                        VStack(alignment: .leading, spacing: 16) {
                            Text("本程序核心原理")
                                .font(.title2.bold())
                                .foregroundColor(.black)

                            VStack(alignment: .leading, spacing: 12) {
                                // 这一大段介绍文本保持原样
                                Label("对象的大小", systemImage: "1.circle.fill")
                                    .font(.headline)
                                    .foregroundColor(.black)
                                Text("利用电子显示屏和chatgpt...")

                                Label("坐标系", systemImage: "2.circle.fill")
                                    .font(.headline)
                                    .foregroundColor(.black)
                                Text("坐标系是有方向和原点的...")

                                Label("绘制直线系统", systemImage: "3.circle.fill")
                                    .font(.headline)
                                    .foregroundColor(.black)
                                Text("必须使用系统底层的绘线技术...")

                                Label("鼠标，键盘专用自定义重写reprentable程序", systemImage: "4.circle.fill")
                                    .font(.headline)
                                    .foregroundColor(.black)
                                Text("所有的 swiftui中的拖拽和hover...")

                                HStack {
                                    Rectangle()
                                        .stroke(style: StrokeStyle(lineWidth: 2, dash: [6, 3]))
                                        .foregroundColor(.red)
                                        .frame(height: 2)

                                    Image(systemName: "star.fill")
                                        .foregroundColor(.red)
                                        .padding(.horizontal, 8)

                                    Rectangle()
                                        .stroke(style: StrokeStyle(lineWidth: 2, dash: [6, 3]))
                                        .foregroundColor(.red)
                                        .frame(height: 2)
                                }
                                .padding(.vertical, 10)

                                Label("业务系统", systemImage: "5.circle.fill")
                                    .font(.headline)
                                    .foregroundColor(.black)
                                Text("有了前面的 4 个核心支撑...")
                            }
                            .font(.subheadline)
                            .foregroundColor(.gray)

                            Button("我知道了") {
                                withAnimation {
                                    showCorePoints = false
                                }
                            }
                            .buttonStyle(.borderedProminent)
                            .tint(.blue)
                        }
                        .padding(20)
                        .background(Color.white.opacity(0.95))
                        .cornerRadius(12)
                        .shadow(radius: 10)
                        .frame(width: width * 0.8, height: height * 0.7)
                        .transition(.scale)
                    }
                    .animation(.easeInOut, value: showCorePoints)
                }

                // 8. 新建节点面板
                if showingAddNodePanel {
                    ZStack {
                        Color.black.opacity(0.5)
                            .ignoresSafeArea()
                            .onTapGesture {
                                showingAddNodePanel = false
                            }

                        AddNodePanelView(
                            newNodeName: $newNodeName,
                            newNodeTypeID: $newNodeTypeID,
                            newNodeWidthMM: $newNodeWidthMM,
                            newNodeHeightMM: $newNodeHeightMM,
                            nodeTypes: nodeTypes,
                            logicCenter: coordinateSystem.pointToCoordinate(
                                screenX: width / 2,
                                screenY: height / 2
                            ),
                            onCreate: {
                                let pos = coordinateSystem.pointToCoordinate(
                                    screenX: width / 2,
                                    screenY: height / 2
                                )
                                self.nodeSystemViewModel.createNode(
                                    x: pos.x,
                                    y: pos.y,
                                    name: newNodeName,
                                    typeID: newNodeTypeID,
                                    width: newNodeWidthMM,
                                    height: newNodeHeightMM
                                ) {
                                    loadNodes { _, _ in
                                        showingAddNodePanel = false
                                    }
                                }
                            },
                            onCancel: {
                                showingAddNodePanel = false
                            },
                            onReloadNodeTypes: {
                                // 这里调用 D3test 自身的 loadNodeTypes
                                if let sid = selectedSystemID {
                                    loadNodeTypes { success, errorMessage in
                                        if success {
                                            print("刷新 nodeTypes 成功")
                                        } else {
                                            print("刷新 nodeTypes 失败: \(errorMessage ?? "")")
                                        }
                                    }
                                }
                            },
                            systemID: Binding(
                                get: { selectedSystemID ?? 1 },
                                set: { selectedSystemID = $0 }
                            )
                            
                        )
                    }
                    .transition(.scale)
                }

                // 9. 新建节点面板(拖拽后)
                if showingNodeCreationPanelAfterDrag {
                    ZStack {
                        Color.black.opacity(0.5)
                            .ignoresSafeArea()
                            .onTapGesture {
                                showingNodeCreationPanelAfterDrag = false
                            }

                        NodeCreationAfterDragPanelView(
                            newNodeName: $newNodeName,
                            newNodeTypeID: $newNodeTypeID,
                            newNodeWidthMM: $newNodeWidthMM,
                            newNodeHeightMM: $newNodeHeightMM,
                            nodeTypes: nodeTypes,
                            pendingConnectionFromNodeID: $pendingConnectionFromNodeID,
                            pendingConnectionPosition: $pendingConnectionPosition,
                            onCreate: { fromID, pos in
                                self.nodeSystemViewModel.createNode(
                                    x: pos.x,
                                    y: pos.y,
                                    name: newNodeName,
                                    typeID: newNodeTypeID,
                                    width: newNodeWidthMM,
                                    height: newNodeHeightMM
                                ) {
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                        if let newNode = nodeSystemViewModel.findNodeByPosition(pos),
                                           let newNodeID = newNode.databaseID
                                        {
                                            nodeSystemViewModel.createConnection(from: fromID, to: newNodeID)
                                        }
                                        pendingConnectionFromNodeID = nil
                                        pendingConnectionPosition = nil
                                    }
                                    loadNodes { _, _ in
                                        showingNodeCreationPanelAfterDrag = false
                                    }
                                }
                            },
                            onCancel: {
                                pendingConnectionFromNodeID = nil
                                pendingConnectionPosition = nil
                                showingNodeCreationPanelAfterDrag = false
                            }
                        )
                    }
                    .transition(.scale)
                }

                // 10. 加载中
                if isLoading && loadError == nil {
                    LoadingOverlay(message: "正在加载数据...")
                }

                // 11. 错误
                if let error = loadError {
                    ErrorOverlay(message: error, onRetry: {
                        if let sid = selectedSystemID {
                            performFullLoad(systemID: sid)
                        }
                    })
                }
            }
            .overlay(richTextEditorViewOverlay(), alignment: .center)
            .onChange(of: nodeSystemViewModel.objects) { _ in
                // 占位回调（原逻辑如无则留空）
            }
            .onReceive(fenceTimer) { _ in
                fenceAnimationPhase += 1
                if fenceAnimationPhase > 15 {
                    fenceAnimationPhase = 0
                }
            }
            .onAppear {
                // 当直接传入 systemID 时，需要触发数据加载
                if let sid = selectedSystemID, nodeSystemViewModel.objects.isEmpty {
                    performFullLoad(systemID: sid)
                }
            }
            .onChange(of: offsetX) { _ in updateViewControlParamsInBackend() }
            .onChange(of: offsetY) { _ in updateViewControlParamsInBackend() }
            .onChange(of: zoomScale) { _ in updateViewControlParamsInBackend() }
            .onChange(of: lineSpacingMM) { _ in updateViewControlParamsInBackend() }
            .onChange(of: showDashedLines) { _ in updateViewControlParamsInBackend() }
            .onChange(of: dashedLineOpacity) { _ in updateViewControlParamsInBackend() }
            .onChange(of: showCoordinateLabels) { _ in updateViewControlParamsInBackend() }
            .onChange(of: coordinateLabelOpacity) { _ in updateViewControlParamsInBackend() }
            .alert("确认删除", isPresented: $showDeleteConfirmation, presenting: nodeToDelete) { node in
                Button("删除", role: .destructive) {
                    nodeSystemViewModel.deleteNode(node: node) {}
                }
                Button("取消", role: .cancel) { }
            } message: { node in
                Text("确定要删除节点 '\(node.name ?? "Unnamed")' 吗？此操作无法撤销。")
            }
        }
    
    
    
    
    
    

    private func richTextEditorViewOverlay() -> some View {
        if let richNodeID = selectedRichTextNodeID, let sid = selectedSystemID {
            if let richNode = nodeSystemViewModel.objects.first(where: { $0.databaseID == richNodeID }) {
                return AnyView(
                    ZStack {
                        RichTextEditorView(
                            nodeID: richNodeID,
                            baseURL: baseURL,
                            authKey: authKey,
                            nodeName: richNode.name ?? "Unnamed",
                            onClose: {
                                selectedRichTextNodeID = nil
                            },
                            systemID: sid
                        )
                        .frame(width: richTextEditorWidth, height: richTextEditorHeight)
                    }
                )
            } else {
                return AnyView(EmptyView())
            }
        } else {
            return AnyView(EmptyView())
        }
    }

    private func handleMainDragEnd(value: DragGesture.Value, width: CGFloat, height: CGFloat, spacingInPixels: CGFloat, spacingInMM: CGFloat) {
        let deltaXInPixels = value.translation.width
        let deltaYInPixels = value.translation.height
        let deltaXInMM = -deltaXInPixels * (spacingInMM / spacingInPixels)
        let deltaYInMM = deltaYInPixels * (spacingInMM / spacingInPixels)

        offsetX += deltaXInMM
        offsetY += deltaYInMM

        dragOffset = .zero
        updateViewControlParamsInBackend()
    }

    private func calculatePixelsPerMM(geometry: GeometryProxy) -> CGFloat {
        let width = geometry.size.width
        let height = geometry.size.height
        let screen = NSScreen.main
        let screenNumber = screen?.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")] as? CGDirectDisplayID

        guard let screenNumber = screenNumber else {
            return CGFloat(3.0)
        }
        let dimensions = CGDisplayScreenSize(screenNumber)
        let screenWidthInMM = dimensions.width
        let screenHeightInMM = dimensions.height
        let screenWidthInPoints = screen?.frame.size.width ?? width
        let screenHeightInPoints = screen?.frame.size.height ?? height
        let pixelsPerMM_X = screenWidthInPoints / screenWidthInMM
        let pixelsPerMM_Y = screenHeightInPoints / screenHeightInMM
        return (pixelsPerMM_X + pixelsPerMM_Y) / 2.0
    }

    private func makeCoordinateSystem(width: CGFloat, height: CGFloat, centerX: CGFloat, centerY: CGFloat, spacingInPixels: CGFloat, spacingInMM: CGFloat, pixelsPerMM: CGFloat) -> CoordinateSystem {
        let deltaXInPixels = dragOffset.width
        let deltaYInPixels = dragOffset.height
        let deltaXInMM = -deltaXInPixels * (spacingInMM / spacingInPixels)
        let deltaYInMM = deltaYInPixels * (spacingInMM / spacingInPixels)

        let adjustedOffsetX = offsetX + deltaXInMM
        let adjustedOffsetY = offsetY + deltaYInMM

        return CoordinateSystem(
            centerX: centerX,
            centerY: centerY,
            spacingInPixels: spacingInPixels,
            spacingInMM: spacingInMM,
            pixelsPerMM: pixelsPerMM,
            zoomScale: zoomScale,
            adjustedOffsetX: adjustedOffsetX,
            adjustedOffsetY: adjustedOffsetY
        )
    }
    
    private func loadSystems(completion: @escaping () -> Void) {
        guard let url = URL(string: "\(NetworkConfig.monitorServerForD3test)/systems") else {
            completion()
            return
        }
        var request = URLRequest(url: url)
        request.setValue(authKey, forHTTPHeaderField: "Authorization")
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("loadSystems error: \(error)")
                    completion()
                    return
                }
                
                guard let data = data else {
                    print("No data for systems")
                    completion()
                    return
                }
                
                if let json = try? JSONSerialization.jsonObject(with: data, options: []),
                   let arr = json as? [[String: Any]] {
                    self.systems = arr.compactMap { dict -> MySystemItem? in
                        guard let id = dict["id"] as? Int,
                              let name = dict["name"] as? String else {
                            return nil
                        }
                        return MySystemItem(
                            id: id,
                            name: name,
                            description: dict["description"] as? String,
                            created_at: dict["created_at"] as? String ?? "",
                            updated_at: dict["updated_at"] as? String ?? ""
                            
                        )
                    }
                }
                completion()
            }
        }.resume()
    }
    
    private func createSystem(name: String, description: String, completion: @escaping ()->Void) {
        guard let url = URL(string: "\(baseURL)/systems") else {
            completion()
            return
        }
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue(authKey, forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let body: [String:Any] = [
            "name": name,
            "description": description
        ]
        request.httpBody = try? JSONSerialization.data(withJSONObject: body, options: [])

        URLSession.shared.dataTask(with: request) { data,response,error in
            DispatchQueue.main.async {
                if let error = error {
                    print("createSystem error: \(error)")
                    completion()
                    return
                }
                completion()
            }
        }.resume()
    }
    func deleteSystem(_ systemID: Int, completion: @escaping (Bool, String?) -> Void) {
        guard let url = URL(string: "\(baseURL)/systems/\(systemID)") else {
            completion(false, "Invalid URL")
            return
        }
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.setValue(authKey, forHTTPHeaderField: "Authorization")

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                DispatchQueue.main.async {
                    completion(false, "删除系统时出错: \(error)")
                }
                return
            }
            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    completion(false, "无效的服务器响应。")
                }
                return
            }
            if httpResponse.statusCode == 200 {
                DispatchQueue.main.async {
                    // 1. 从 viewModel 里删
                    self.systemVM.systems.removeAll { $0.id == systemID }

                    // 或者写：self.systemVM.fetchSystems()
                    completion(true, nil)
                }
            } else {
                DispatchQueue.main.async {
                    completion(false, "服务器返回错误码 \(httpResponse.statusCode)")
                }
            }
        }.resume()
    }

    private func performFullLoad(systemID: Int) {
        nodeSystemViewModelHolder.value = NodeSystemViewModel(
            baseURL: NetworkConfig.monitorServerForD3test,
            authKey: authKey,
            systemID: systemID
        )
        loadError = nil
        isLoading = true

        loadViewControlParams { success, errorMessage in
            guard success else {
                DispatchQueue.main.async {
                    self.loadError = errorMessage ?? "加载视图参数失败"
                    self.isLoading = false
                }
                return
            }

            loadNodeTypes { success, errorMessage in
                guard success else {
                    DispatchQueue.main.async {
                        self.loadError = errorMessage ?? "加载节点类型失败"
                        self.isLoading = false
                    }
                    return
                }

                loadNodes { success, errorMessage in
                    guard success else {
                        DispatchQueue.main.async {
                            self.loadError = errorMessage ?? "加载节点数据失败"
                            self.isLoading = false
                        }
                        return
                    }

                    loadConnections { success, errorMessage in
                        DispatchQueue.main.async {
                            if success {
                                self.isLoading = false
                            } else {
                                self.loadError = errorMessage ?? "加载连接数据失败"
                                self.isLoading = false
                            }
                        }
                    }
                }
            }
        }
    }

    private func loadViewControlParams(completion: @escaping (Bool, String?) -> Void) {
        guard let sid = selectedSystemID,
              let url = URL(string: "\(NetworkConfig.monitorServerForD3test)/view_control_params?system_id=\(sid)") else {
            completion(false, "无效URL")
            return
        }

        var request = URLRequest(url: url)
        request.setValue(authKey, forHTTPHeaderField: "Authorization")

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(false, "加载视图参数错误: \(error.localizedDescription)")
                return
            }
            guard let httpResponse = response as? HTTPURLResponse,
                  (200...299).contains(httpResponse.statusCode) else {
                completion(false, "加载视图参数失败: 无效的响应状态码")
                return
            }
            guard let data = data else {
                completion(false, "加载视图参数失败: 无数据")
                return
            }

            if let json = try? JSONSerialization.jsonObject(with: data, options: []),
               let dict = json as? [String: Any] {
                DispatchQueue.main.async {
                    if let id = dict["id"] as? Int {
                        self.viewControlParamsID = id
                    }
                    if let val = dict["zoom_scale"] as? Double {
                        self.zoomScale = CGFloat(val)
                    }
                    if let val = dict["offset_x"] as? Double {
                        self.offsetX = CGFloat(val)
                    }
                    if let val = dict["offset_y"] as? Double {
                        offsetY = CGFloat(val)
                    }
                    if let val = dict["line_spacing_mm"] as? Double {
                        lineSpacingMM = val
                    }
                    if let val = dict["show_dashed_lines"] as? Int {
                        showDashedLines = (val == 1)
                    }
                    if let val = dict["dashed_line_opacity"] as? Double {
                        dashedLineOpacity = val
                    }
                    if let val = dict["show_coordinate_labels"] as? Int {
                        showCoordinateLabels = (val == 1)
                    }
                    if let val = dict["coordinate_label_opacity"] as? Double {
                        coordinateLabelOpacity = val
                    }
                }
                
                completion(true, nil)
            } else {
                completion(false, "视图参数数据格式错误")
            }
        }.resume()
    }

    func updateViewControlParamsInBackend() {
        guard let sid = selectedSystemID else {
            return
        }
        guard let id = viewControlParamsID else {
            createViewControlParams(systemID: sid)
            return
        }

        guard let url = URL(string: "\(baseURL)/view_control_params/\(id)") else { return }
        var request = URLRequest(url: url)
        request.httpMethod = "PUT"
        request.setValue(authKey, forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let body: [String: Any] = [
            "system_id": sid,
            "zoom_scale": Double(zoomScale),
            "offset_x": Double(offsetX),
            "offset_y": Double(offsetY),
            "line_spacing_mm": lineSpacingMM,
            "show_dashed_lines": showDashedLines,
            "dashed_line_opacity": dashedLineOpacity,
            "show_coordinate_labels": showCoordinateLabels,
            "coordinate_label_opacity": coordinateLabelOpacity
        ]

        request.httpBody = try? JSONSerialization.data(withJSONObject: body, options: [])

        URLSession.shared.dataTask(with: request) { _, response, error in
            if let error = error {
                print("updateViewControlParamsInBackend error: \(error)")
            }
        }.resume()
    }

    func createViewControlParams(systemID: Int) {
        guard let url = URL(string: baseURL + NetworkConfig.Endpoints.viewControlParams) else { return }
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue(authKey, forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let body: [String: Any] = [
            "system_id": systemID,
            "zoom_scale": Double(zoomScale),
            "offset_x": Double(offsetX),
            "offset_y": Double(offsetY),
            "line_spacing_mm": lineSpacingMM,
            "show_dashed_lines": showDashedLines,
            "dashed_line_opacity": dashedLineOpacity,
            "show_coordinate_labels": showCoordinateLabels,
            "coordinate_label_opacity": coordinateLabelOpacity,
            "god_view_height": 1000.0
        ]

        request.httpBody = try? JSONSerialization.data(withJSONObject: body, options: [])

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("createViewControlParams error: \(error)")
                return
            }
            guard let data = data else {
                print("createViewControlParams no data returned.")
                return
            }
            if let resp = response as? HTTPURLResponse {
                print("createViewControlParams HTTP status: \(resp.statusCode)")
            }
            if let json = try? JSONSerialization.jsonObject(with: data, options: []),
               let dict = json as? [String: Any], let rid = dict["id"] as? Int {
                DispatchQueue.main.async {
                    viewControlParamsID = rid
                }
            }
        }.resume()
    }

    // ============== 以下是 loadNodes( ) 的改动 ==============
        func loadNodes(completion: @escaping (Bool, String?) -> Void) {
            guard let sid = selectedSystemID,
                  let url = URL(string: baseURL + NetworkConfig.Endpoints.nodes + "?system_id=\(sid)") else {
                completion(false, "无效URL")
                return
            }
            
            var request = URLRequest(url: url)
            request.setValue(authKey, forHTTPHeaderField: "Authorization")
            
            URLSession.shared.dataTask(with: request) { data, response, error in
                if let error = error {
                    completion(false, "加载节点数据错误: \(error.localizedDescription)")
                    return
                }
                guard let httpResponse = response as? HTTPURLResponse,
                      (200...299).contains(httpResponse.statusCode) else {
                    completion(false, "加载节点数据失败: 无效的响应状态码")
                    return
                }
                guard let data = data else {
                    completion(false, "加载节点数据失败: 无数据")
                    return
                }

                if let jsonArray = try? JSONSerialization.jsonObject(with: data, options: []) as? [[String: Any]] {
                    DispatchQueue.main.async {
                        withAnimation {
                            nodeSystemViewModel.objects = jsonArray.compactMap { dict -> GraphicObject? in
                                let idString = "\(dict["id"] ?? "")"
                                let xString = "\(dict["x"] ?? "")"
                                let yString = "\(dict["y"] ?? "")"
                                let widthString = "\(dict["width"] ?? "")"
                                let heightString = "\(dict["height"] ?? "")"

                                guard let id = Int(idString),
                                      let x = Double(xString),
                                      let y = Double(yString),
                                      let width = Double(widthString),
                                      let height = Double(heightString) else {
                                    print("Skipping a node due to field mismatch: \(dict)")
                                    return nil
                                }

                                let nodeTypeString = "\(dict["node_type_id"] ?? "1")"
                                let nodeTypeID = Int(nodeTypeString) ?? 1

                                // 原先 shape 逻辑
                                let shape: GraphicObject.ShapeType = (nodeTypeID % 2 == 0) ? .circle : .rectangle

                                // ★ 新增：优先判断富文本节点 ID=9，其它则看 colorHex
                                var color: Color
                                if nodeTypeID == 9 {
                                    color = .yellow  // 保留原富文本节点颜色
                                } else {
                                    if let foundType = self.nodeTypes.first(where: { $0.id == nodeTypeID }),
                                       let hexStr = foundType.colorHex,
                                       let parsedColor = Color(hex: hexStr)
                                    {
                                        color = parsedColor
                                    } else {
                                        // fallback 颜色
                                        color = .blue
                                    }
                                }

                                let name = dict["name"] as? String

                                let go = GraphicObject(
                                    databaseID: id,
                                    position: CGPoint(x: CGFloat(x), y: CGFloat(y)),
                                    size: CGSize(width: CGFloat(width), height: CGFloat(height)),
                                    color: color,
                                    shape: shape,
                                    name: name,
                                    nodeTypeID: nodeTypeID
                                )
                                return go
                            }
                        }
                    }
                    completion(true, nil)
                 } else {
                    completion(false, "节点数据格式错误")
                 }
            }.resume()
        }
    
    
    func loadNodeTypes(completion: @escaping (Bool, String?) -> Void) {
         guard let sid = selectedSystemID,
               let url = URL(string: baseURL + NetworkConfig.Endpoints.nodeTypes + "?system_id=\(sid)") else {
             completion(false, "无效URL")
             return
         }
         
         var request = URLRequest(url: url)
         request.setValue(authKey, forHTTPHeaderField: "Authorization")

         URLSession.shared.dataTask(with: request) { data, response, error in
             if let error = error {
                 completion(false, "加载节点类型错误: \(error.localizedDescription)")
                 return
             }
             guard let httpResponse = response as? HTTPURLResponse,
                   (200...299).contains(httpResponse.statusCode) else {
                 completion(false, "加载节点类型失败: 无效的响应状态码")
                 return
             }
             guard let data = data else {
                 completion(false, "加载节点类型失败: 无数据")
                 return
             }

             // ★ 把 color_hex 存到 NodeTypeItem
             if let jsonArray = try? JSONSerialization.jsonObject(with: data, options: []) as? [[String: Any]] {
                 let types = jsonArray.compactMap { dict -> NodeTypeItem? in
                     guard let id = dict["id"] as? Int,
                           let name = dict["name"] as? String else {
                         return nil
                     }
                     let description = dict["description"] as? String
                     let colorHex = dict["color_hex"] as? String  // 新增
                     return NodeTypeItem(id: id, name: name, description: description, colorHex: colorHex)
                 }
                 DispatchQueue.main.async {
                     self.nodeTypes = types
                 }
                 
                 completion(true, nil)
             } else {
                 completion(false, "节点类型数据格式错误")
             }
         }.resume()
     }
    
    

    func loadConnections(completion: @escaping (Bool, String?) -> Void) {
        guard let sid = selectedSystemID,
              let url = URL(string: baseURL + NetworkConfig.Endpoints.connections + "?system_id=\(sid)") else {
            completion(false, "无效URL")
            return
        }
        var request = URLRequest(url: url)
        request.setValue(authKey, forHTTPHeaderField: "Authorization")

        URLSession.shared.dataTask(with: request) { data, response, error in
                if let error = error {
                    completion(false, "加载连接数据错误: \(error.localizedDescription)")
                    return
                }
                guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                    completion(false, "加载连接数据失败: 无效的响应状态码")
                    return
                }
                guard let data = data else {
                    completion(false, "加载连接数据失败: 无数据")
                    return
                }

            if let jsonArray = try? JSONSerialization.jsonObject(with: data, options: []) as? [[String: Any]] {
                let conns = jsonArray.compactMap { dict -> Connection? in
                    let idString = "\(dict["id"] ?? "")"
                    let fromString = "\(dict["from_node_id"] ?? "")"
                    let toString = "\(dict["to_node_id"] ?? "")"
                    let connType = dict["connection_type"] as? String

                    guard let id = Int(idString),
                          let fromID = Int(fromString),
                          let toID = Int(toString) else {
                        return nil
                    }

                    return Connection(id: id, fromNodeID: fromID, toNodeID: toID, connectionType: connType)
                }
                DispatchQueue.main.async {
                    nodeSystemViewModel.connections = conns
                }
                completion(true, nil)
            } else {
                completion(false, "连接数据格式错误")
            }
        }.resume()
    }
}

// AddNodePanelView.swift (更新后的代码)
// AddNodePanelView.swift (更新后的代码)
import SwiftUI

struct AddNodePanelView: View {
    @Binding var newNodeName: String
    @Binding var newNodeTypeID: Int
    @Binding var newNodeWidthMM: Double
    @Binding var newNodeHeightMM: Double
    var nodeTypes: [NodeTypeItem]
    var logicCenter: CGPoint

    /// 点击"创建"后，把新节点的逻辑交给上层处理
    var onCreate: ()->Void

    /// 点击"取消"后，关闭弹窗
    var onCancel: ()->Void

    /// **新增**：PUT 成功后，调用此回调来刷新 nodeTypes。
    /// 你可以在父级(D3test)里实现该回调，内部调用 `loadNodeTypes { ... }`。
    var onReloadNodeTypes: ()->Void

    // ~~~~~~~~~~~ 下方与之前类似 ~~~~~~~~~~~

    // 记录用户是否要修改类型名称和描述
    @State private var editedTypeName: String = ""
    @State private var originalTypeName: String = ""
    @State private var editedTypeDescription: String = ""
    @State private var originalTypeDescription: String = ""

    // 需要访问后端地址和 authKey，或从外部注入
    let baseURL: String = NetworkConfig.monitorServerForD3test
    let authKey: String = "13795375098"

    // 只要改成 @Binding 即可
    @Binding var systemID: Int

    var body: some View {
        VStack(spacing: 20) {
            Text("新建节点")
                .font(.title)
                .fontWeight(.bold)
                .padding(.top, 20)
            
            TextField("节点名称", text: $newNodeName)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .padding([.leading, .trailing], 20)

            // 选择节点类型
            Picker("节点类型（选择）", selection: $newNodeTypeID) {
                ForEach(nodeTypes) { nt in
                    Text(nt.name).tag(nt.id)
                }
            }
            .pickerStyle(MenuPickerStyle())
            .padding([.leading, .trailing], 20)
            .onChange(of: newNodeTypeID) { newValue in
                // 当用户切换类型时，把对应的原名称、原描述设置到编辑中的State里
                if let selectedType = nodeTypes.first(where: { $0.id == newValue }) {
                    editedTypeName = selectedType.name
                    originalTypeName = selectedType.name

                    let desc = selectedType.description ?? ""
                    editedTypeDescription = desc
                    originalTypeDescription = desc
                }
            }

            // 修改该类型名称
            HStack {
                Text("修改该类型名称:")
                TextField("在这里改类型名", text: $editedTypeName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(width: 150)
            }

            // 修改该类型描述
            HStack {
                Text("修改该类型描述:")
                TextField("在这里改类型描述", text: $editedTypeDescription)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(width: 150)
            }

            HStack {
                Text("宽度(mm)")
                TextField("宽度(mm)", value: $newNodeWidthMM, format: .number)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(width: 80)
            }
            
            HStack {
                Text("高度(mm)")
                TextField("高度(mm)", value: $newNodeHeightMM, format: .number)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(width: 80)
            }
            
            HStack(spacing: 30) {
                Button("创建") {
                    // 当用户点击创建时，先检查是否修改了类型名称或描述
                    let nameChanged = editedTypeName.trimmingCharacters(in: .whitespaces) != originalTypeName.trimmingCharacters(in: .whitespaces)
                    let descChanged = editedTypeDescription.trimmingCharacters(in: .whitespaces) != originalTypeDescription.trimmingCharacters(in: .whitespaces)
                    
                    // 如果两者都没变，就直接创建节点；否则先更新类型
                    if nameChanged || descChanged {
                        if let typeID = nodeTypes.first(where: { $0.id == newNodeTypeID })?.id {
                            updateNodeType(typeID: typeID,
                                           newName: editedTypeName,
                                           newDesc: editedTypeDescription) {
                                // 更新完后再创建节点
                                onCreate()
                            }
                        } else {
                            // 没找到typeID，直接创建
                            onCreate()
                        }
                    } else {
                        // 类型名和描述都没改，直接创建
                        onCreate()
                    }
                }
                .buttonStyle(.borderedProminent)
                .tint(.blue)
                .font(.system(size: 16, weight: .bold))
                
                Button("取消") {
                    onCancel()
                }
                .buttonStyle(.bordered)
                .font(.system(size: 16))
            }
            .padding(.bottom, 20)
        }
        .frame(width: 360)
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(Color.white.opacity(0.9))
                .shadow(radius: 10)
        )
        .padding()
        .onAppear {
            // 确保初次显示时也能同步 selectedType
            if let selectedType = nodeTypes.first(where: { $0.id == newNodeTypeID }) {
                editedTypeName = selectedType.name
                originalTypeName = selectedType.name

                let desc = selectedType.description ?? ""
                editedTypeDescription = desc
                originalTypeDescription = desc
            }
        }
    }

    // 调用后端的PUT来更新node_types表的name和description字段
    // 在成功后，再次调用 onReloadNodeTypes() 以从后端拉取最新类型，更新UI
    func updateNodeType(typeID: Int, newName: String, newDesc: String, completion: @escaping ()->Void) {
        guard let url = URL(string: "\(baseURL)/node_types/\(typeID)") else {
            completion()
            return
        }
        var request = URLRequest(url: url)
        request.httpMethod = "PUT"
        request.setValue(authKey, forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // 1. 查找原本的 colorHex
        let originalColorHex = nodeTypes.first(where: { $0.id == typeID })?.colorHex
        
        // 2. 把它放进 body
        let body: [String: Any?] = [
            "system_id": systemID,
            "name": newName,
            "description": newDesc,
            "color_hex": originalColorHex  // 即使是nil，这里也显式写上
        ]
        // 注意：这是 [String: Any?]，最后要在序列化时进行处理

        // 3. 转成 JSON
        let validBody = body.compactMapValues { $0 }  // 如果 originalColorHex=nil，这行可让它写成 "color_hex": null
        request.httpBody = try? JSONSerialization.data(withJSONObject: validBody, options: [])

        URLSession.shared.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("updateNodeType error: \(error)")
                    completion()
                    return
                }
                if let httpResponse = response as? HTTPURLResponse {
                    if (200...299).contains(httpResponse.statusCode) {
                        // 成功
                        completion()
                        onReloadNodeTypes()  // 再刷新 nodeTypes
                    } else {
                        print("updateNodeType failed with status: \(httpResponse.statusCode)")
                        completion()
                    }
                } else {
                    // 未知错误
                    completion()
                }
            }
        }.resume()
    }
}


struct NodeCreationAfterDragPanelView: View {
    @Binding var newNodeName: String
    @Binding var newNodeTypeID: Int
    @Binding var newNodeWidthMM: Double
    @Binding var newNodeHeightMM: Double
    var nodeTypes: [NodeTypeItem]
    @Binding var pendingConnectionFromNodeID: Int?
    @Binding var pendingConnectionPosition: CGPoint?
    var onCreate: (_ fromID: Int, _ pos: CGPoint)->Void
    var onCancel: ()->Void
    
    var body: some View {
        VStack(spacing: 20) {
            Text("新建节点")
                .font(.title)
                .fontWeight(.bold)
                .padding(.top, 20)
            
            TextField("节点名称", text: $newNodeName)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .padding([.leading, .trailing], 20)
            
            Picker("节点类型（选择）", selection: $newNodeTypeID) {
                ForEach(nodeTypes) { nt in
                    Text(nt.name).tag(nt.id)
                }
            }
            .pickerStyle(MenuPickerStyle())
            .padding([.leading, .trailing], 20)
            
            HStack {
                Text("宽度(mm)")
                TextField("宽度(mm)", value: $newNodeWidthMM, format: .number)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(width: 80)
            }
            
            HStack {
                Text("高度(mm)")
                TextField("高度(mm)", value: $newNodeHeightMM, format: .number)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(width: 80)
            }
            
            HStack(spacing: 30) {
                Button("创建") {
                    if let fromID = pendingConnectionFromNodeID,
                       let pos = pendingConnectionPosition {
                        onCreate(fromID, pos)
                    } else {
                        onCancel()
                    }
                }
                .buttonStyle(.borderedProminent)
                .tint(.blue)
                .font(.system(size: 16, weight: .bold))
                
                Button("取消") {
                    onCancel()
                }
                .buttonStyle(.bordered)
                .font(.system(size: 16))
            }
            .padding(.bottom, 20)
        }
        .frame(width: 300)
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(Color.white.opacity(0.9))
                .shadow(radius: 10)
        )
        .padding()
    }
}


struct SliderWithLabel: View {
    @Binding var value: Double
    let range: ClosedRange<Double>
    let step: Double
    
    var body: some View {
        VStack(spacing: 5) {
            Text("\(Int(value))mm")
                .font(.system(size: 14))
                .foregroundColor(.black)
            
            Slider(value: $value, in: range, step: step)
                .accentColor(.blue)
        }
        .padding(10)
        .background(Color.white.opacity(0.9))
        .cornerRadius(10)
        .shadow(radius: 5)
    }
}

/// 控制面板拆分出的子视图
/// 用于调节 lineSpacingMM、showDashedLines、coordinateLabelOpacity 等参数
struct ControlPanelView: View {
    // 视情况使用 @Binding/@ObservedObject 引入你在 D3test 中已有的相关参数
    // 以下仅示例常用的几个
    @Binding var lineSpacingMM: Double
    @Binding var showDashedLines: Bool
    @Binding var dashedLineOpacity: Double
    @Binding var showCoordinateLabels: Bool
    @Binding var coordinateLabelOpacity: Double
    @Binding var showCorePoints: Bool

    var body: some View {
        HStack(spacing: 100) {
            // 这里是你原先的「调整虚线间隔」UI
            // （如减号按钮、滑条、加号按钮、恢复默认按钮等）
            VStack(spacing: 10) {
                Text("调整虚线间隔")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.black)
                    .padding(.bottom, 5)

                HStack(spacing: 10) {
                    Button(action: {
                        lineSpacingMM = max(lineSpacingMM - 1, 10)
                    }) {
                        Circle()
                            .fill(Color.blue)
                            .frame(width: 30, height: 30)
                            .overlay(
                                Text("-")
                                    .font(.system(size: 20))
                                    .foregroundColor(.white)
                            )
                    }

                    // 你原先的滑条，可以封装成一个小视图
                    Slider(value: $lineSpacingMM, in: 10...100, step: 1)
                        .accentColor(.blue)
                        .frame(width: 150)

                    Button(action: {
                        lineSpacingMM = min(lineSpacingMM + 1, 100)
                    }) {
                        Circle()
                            .fill(Color.blue)
                            .frame(width: 30, height: 30)
                            .overlay(
                                Text("+")
                                    .font(.system(size: 20))
                                    .foregroundColor(.white)
                            )
                    }

                    Button("恢复默认") {
                        lineSpacingMM = 50
                    }
                    .font(.system(size: 14))
                    .foregroundColor(.black)
                    .buttonStyle(.bordered)
                }
            }
            .padding(10)
            .background(Color.white.opacity(0.9))
            .cornerRadius(10)
            .shadow(radius: 5)
            .opacity(showDashedLines ? 1 : 0)
            .animation(.easeInOut(duration: 0.3), value: showDashedLines)

            // 这里是你原先的 「显示虚线」「显示坐标」 Toggle
            VStack(alignment: .leading, spacing: 10) {
                Toggle(isOn: $showDashedLines) {
                    HStack {
                        Image(systemName: "eye")
                            .resizable()
                            .frame(width: 20, height: 15)
                            .foregroundColor(.blue)
                        Text("显示虚线")
                            .font(.system(size: 14))
                            .foregroundColor(.black)
                    }
                }.toggleStyle(SwitchToggleStyle(tint: .blue))

                Toggle(isOn: $showCoordinateLabels) {
                    HStack {
                        Image(systemName: "number.circle")
                            .resizable()
                            .frame(width: 20, height: 20)
                            .foregroundColor(.blue)
                        Text("显示坐标")
                            .font(.system(size: 14))
                            .foregroundColor(.black)
                    }
                }.toggleStyle(SwitchToggleStyle(tint: .blue))
            }
            .padding(10)
            .background(Color.white.opacity(0.9))
            .cornerRadius(10)
            .shadow(radius: 5)

            // 这里是 你原先的「虚线透明度」「坐标透明度」部分
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "circle.lefthalf.fill")
                        .resizable()
                        .frame(width: 25, height: 25)
                        .foregroundColor(.blue)
                    Text("虚线透明度:")
                        .font(.system(size: 14))
                        .foregroundColor(.black)
                    Slider(value: $dashedLineOpacity, in: 0...1)
                        .frame(width: 100)
                    Text(String(format: "%.2f", dashedLineOpacity))
                        .font(.system(size: 14))
                        .foregroundColor(.black)
                }
                HStack {
                    Image(systemName: "circle.righthalf.fill")
                        .resizable()
                        .frame(width: 25, height: 25)
                        .foregroundColor(.blue)
                    Text("坐标透明度:")
                        .font(.system(size: 14))
                        .foregroundColor(.black)
                    Slider(value: $coordinateLabelOpacity, in: 0...1)
                        .frame(width: 100)
                    Text(String(format: "%.2f", coordinateLabelOpacity))
                        .font(.system(size: 14))
                        .foregroundColor(.black)
                }
            }
            .padding(10)
            .background(Color.white.opacity(0.9))
            .cornerRadius(10)
            .shadow(radius: 5)
            .opacity(showDashedLines ? 1 : 0)
            .animation(.easeInOut(duration: 0.3), value: showDashedLines)
        }
    }
}

/// 将「系统选择」和「新建系统」逻辑合并为一个 overlay 视图
/// 你原来的 SelectSystemView、CreateSystemPanelView 保持不变也行，
/// 这里只是演示如何把那段 ZStack { ... } 抽成一个子视图。
struct SelectSystemOverlay: View {
    // 原本 D3test 里对应的 @State / @Binding 变量
    @Binding var systems: [MySystemItem]
    @Binding var selectedSystemID: Int?
    @Binding var showSystemSelection: Bool
    @Binding var showingCreateSystemPanel: Bool
    // 以及新建系统时用到的文本字段
    @Binding var newSystemName: String
    @Binding var newSystemDescription: String

    // 加载和创建系统的动作，如果你之前是直接在D3test里写的函数，也可用回调
    let onConfirmSelectSystem: ()->Void
    let onCreateSystem: ()->Void

    // 新增：用来执行删除逻辑的回调，而不是在子视图里写死deleteSystem
    var onDeleteSystem: (Int)->Void
    // 新增传入 systemVM
    @ObservedObject var systemVM: SystemViewModel
    var body: some View {
        ZStack {
            Color.black.opacity(0.5).ignoresSafeArea()

            if showingCreateSystemPanel {
                // 你原来的 CreateSystemPanelView
                CreateSystemPanelView(
                    systemName: $newSystemName,
                    systemDescription: $newSystemDescription,
                    onCreate: {
                        onCreateSystem()
                    },
                    onCancel: {
                        showingCreateSystemPanel = false
                    }
                )
            } else {
                // 你原来的 SelectSystemView
                EnhancedSelectSystemView(
                    viewModel: systemVM,
                    selectedSystemID: $selectedSystemID,
                    onSelect: {
                        if selectedSystemID != nil {
                            showSystemSelection = false
                            onConfirmSelectSystem()
                        }
                    },
                    onCreateNewSystem: {
                        showingCreateSystemPanel = true
                    },
                    onDeleteSystem: { sid in

                        onDeleteSystem(sid)
                    }
                )
            }
        }
    }
}

/// 富文本编辑器 overlay 视图
/// 用于在选中了富文本节点后，弹出 RichTextEditorView
struct MyRichTextEditorOverlay: View {
    // 注意：把你 D3test 里需要的 @State / @Binding 都注入进来
    let richNodeID: Int
    let systemID: Int
    let nodeName: String
    let baseURL: String
    let authKey: String

    // 关闭时的回调
    let onClose: ()->Void

    // 编辑器宽高
    var editorWidth: CGFloat
    var editorHeight: CGFloat

    var body: some View {
        ZStack {
            // 你若想要个半透明背景，可以加:
            Color.black.opacity(0.2).ignoresSafeArea()

            RichTextEditorView(
                nodeID: richNodeID,
                baseURL: baseURL,
                authKey: authKey,
                nodeName: nodeName,
                onClose: {
                    onClose()
                },
                systemID: systemID
            )
            .frame(width: editorWidth, height: editorHeight)
        }
    }
}

// 一个用来持有可变ViewModel的简单包装类
class ObservableObjectHolder<T: ObservableObject>: ObservableObject {
    var value: T?
}
