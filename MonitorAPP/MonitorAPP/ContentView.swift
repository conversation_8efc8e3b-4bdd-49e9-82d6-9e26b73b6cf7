import SwiftUI
import WebKit
import AppKit
import Foundation
import ZIPFoundation
import LocalAuthentication


// 主视图

struct CustomButton: View {
    let title: String
    let action: () -> Void
    let isSelected: Bool // 新增参数

    var body: some View {
        Button(action: action) {
            Text(title)
                .frame(maxWidth: .infinity)
                .padding()
                .background(isSelected ? Color.green : Color.blue) // 使用 isSelected 控制背景颜色
                .foregroundColor(.white)
                .cornerRadius(10)
        }
    }
}



struct ContentView: View {
    @State private var selectedView: String?
    @State private var showDifficultiesView = false
    @State private var previousSelectedView: String? // 记住之前选择的视图
    @State private var isInTaskSystem = false // 标记是否在任务系统中
    @State private var isTaskSystemSelected = false // 标记任务系统是否被选中
    

    @State private var isAuthenticated = false
    @Environment(\.scenePhase) var scenePhase
    @State private var authenticationErrorMessage: String?
    @State private var isAuthenticating = false
    @State private var hasAuthenticatedOnce = false // 新增标志位，防止多次触发身份验证

    
    @State private var showPasswordInputView = false
    @State private var password: String = ""
    
    // 添加一个 @State 属性来持有 D3Test 窗口控制器
    @State private var d3TestWindowController: NSWindowController?
    
    @StateObject private var uploadManager = UploadManager()
    
    // 文件列表
    @State private var files: [FileRecord_20240823] = []
    
    // 警告相关
    @State private var alertMessage: String = ""
    @State private var showAlert: Bool = false
    
    var body: some View {
        Group {
            if isAuthenticated {
                VStack(spacing: 0) {
                    GeometryReader { geometry in
                        VStack {
                            HStack(spacing: 16) {
                                Image(systemName: "info.circle.fill")
                                    .font(.system(size: 28))
                                    .foregroundColor(.white)
                                
                                VStack(alignment: .leading, spacing: 6) {
                                    Text("程序说明")
                                        .font(.system(size: 18, weight: .bold))
                                        .foregroundColor(.white)
                                    Text("本程序为日常习惯使用的程序，收集或者更新自己喜欢的常用程序，不影响任何开发进程。")
                                        .font(.system(size: 16))
                                        .foregroundColor(.white)
                                        .lineLimit(2)
                                }
                            }
                            .padding(20)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color.blue.opacity(0.8), Color.purple.opacity(0.8)]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(16)
                            .shadow(color: Color.black.opacity(0.2), radius: 8, x: 0, y: 4)
                            .frame(width: geometry.size.width * 0.8)
                        }
                        .frame(width: geometry.size.width, height: 100)
                        .position(x: geometry.size.width / 2, y: 50)
                    }
                    .frame(height: 100)
                    
                    HStack(spacing: 20) {
                        Spacer()
                        
                        CustomButton1(title: "监视器管理", systemImage: "desktopcomputer", action: {
                            previousSelectedView = selectedView
                            selectedView = "Monitor"
                            isInTaskSystem = false
                            isTaskSystemSelected = false
                        }, isSelected: selectedView == "Monitor")
                        .font(.title)
                        .padding()
                        .background(selectedView == "Monitor" ? Color.green : Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                        .shadow(radius: 5)

                        CustomButton1(title: "一图多用", systemImage: "hammer", action: {
                            previousSelectedView = selectedView
                            selectedView = nil
                            isInTaskSystem = true
                            isTaskSystemSelected = true
                            showD3TestWindow()
                        }, isSelected: isTaskSystemSelected)
                        .font(.title)
                        .padding()
                        .background(isTaskSystemSelected ? Color.green : Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                        .shadow(radius: 5)
                        
                        Spacer()
                    }
                    .padding(.horizontal)
                    .padding(.top, 10)
                    .background(Color(NSColor.windowBackgroundColor))
                    .fixedSize(horizontal: false, vertical: true)
                    
                    Divider()
                    
                    ScrollView {
                        VStack {
                            if selectedView == "Monitor" {
                                MonitorView(uploadManager: uploadManager, onUploadComplete: loadFiles)
                            } else {
                                Text("请选择一个功能")
                                    .font(.largeTitle)
                                    .foregroundColor(.secondary)
                                    .padding()
                            }
                        }
                        .frame(minHeight: 600)
                    }
                }
                .frame(minWidth: 800, minHeight: 600)
                
            } else {
                if isAuthenticating {
                    Text("正在验证用户身份...")
                        .font(.headline)
                        .foregroundColor(.gray)
                } else if let errorMessage = authenticationErrorMessage {
                    Text(errorMessage)
                        .font(.headline)
                        .foregroundColor(.red)
                } else {
                    Text("")
                        .font(.headline)
                        .foregroundColor(.gray)
                }
                
                if showPasswordInputView {
                    VStack {
                        SecureField("请输入密码", text: $password)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .padding()
                        
                        Button("验证") {
                            authenticationErrorMessage = "暂不支持密码验证，需要生物验证。"
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.large)
                        .padding()
                    }
                }
            }
        }
        .onAppear {
            if !hasAuthenticatedOnce {
                hasAuthenticatedOnce = true
                authenticateUser()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSApplication.didBecomeActiveNotification)) { _ in
            print("App became active")
            if !isAuthenticated && !isAuthenticating {
                authenticateUser()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSApplication.didResignActiveNotification)) { _ in
            print("App resigned active")
            isAuthenticated = false
        }
        .onReceive(NotificationCenter.default.publisher(for: .didCloseSystemWindow)) { _ in
            print("Received didCloseSystemWindow notification. Current state - isInTaskSystem: \(isInTaskSystem), selectedView: \(selectedView ?? "nil")")
            
            if isInTaskSystem {
                // 如果当前在任务系统中（即从D3test返回），则重新打开任务选择系统界面
                print("从D3test返回，重新打开任务选择系统界面")
                // 在重新显示窗口之前，确保旧的窗口控制器被释放
                self.d3TestWindowController = nil
                showD3TestWindow() // 重新调用以显示任务选择窗口
                self.isInTaskSystem = false // 重置状态，避免重复触发
            } else {
                if let prevView = previousSelectedView {
                    selectedView = prevView
                    print("Restored to previous view: \(prevView)")
                } else {
                    selectedView = nil
                    print("No previous view found, returning to selection screen")
                }
            }
        }
    }
    
    func loadFiles() {
        DispatchQueue.main.async {
            fetchFilesFromServer { fetchedFiles in
                DispatchQueue.main.async {
                    self.files = fetchedFiles
                    print("文件加载完成，文件列表已更新")
                }
            }
        }
    }
    
    func fetchFilesFromServer(completion: @escaping ([FileRecord_20240823]) -> Void) {
        let url = URL(string: NetworkConfig.mainServer + NetworkConfig.Endpoints.getFiles)!
        URLSession.shared.dataTask(with: url) { data, response, error in
            guard let data = data, error == nil else {
                print("Error fetching files: \(error?.localizedDescription ?? "Unknown error")")
                DispatchQueue.main.async { completion([]) }
                return
            }
            do {
                let filesResponse = try JSONDecoder().decode([FileRecord_20240823].self, from: data)
                completion(filesResponse)
            } catch {
                DispatchQueue.main.async {
                    self.alertMessage = "解析文件列表失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
            }
        }.resume()
    }
    
    func showD3TestWindow() {
        // 每次都创建一个新的窗口，确保状态干净
        let window = NSWindow(
            contentRect: NSScreen.main?.frame ?? NSRect(x: 0, y: 0, width: 800, height: 600),
            styleMask: [.titled, .closable, .resizable, .fullSizeContentView],
            backing: .buffered,
            defer: false
        )
        
        let d3testView = D3test(onReturn: {
            window.close()
            print("从D3test返回，关闭当前窗口。")
        })
        
        window.contentView = NSHostingView(rootView: d3testView)
        window.title = "任务系统 - 一图多用"
        window.isReleasedWhenClosed = false
        window.makeKeyAndOrderFront(nil)
        window.setFrame(NSScreen.main?.visibleFrame ?? NSRect(x: 0, y: 0, width: 800, height: 600), display: true, animate: true)
        
        self.d3TestWindowController = NSWindowController(window: window)
    }
    
    private func validatePassword() {
        if password == "921020" {
            isAuthenticated = true
            showPasswordInputView = false
            authenticationErrorMessage = nil
        } else {
            authenticationErrorMessage = "密码错误，请重试。"
        }
    }
    
    private func authenticateUser() {
        isAuthenticating = true
        authenticationErrorMessage = nil
        
        let context = LAContext()
        var error: NSError?
        
        if context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) {
            let reason = "请验证您的身份以使用本程序。"
            
            context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: reason) { success, authenticationError in
                DispatchQueue.main.async {
                    self.isAuthenticating = false
                    
                    if success {
                        self.isAuthenticated = true
                        self.authenticationErrorMessage = nil
                    } else {
                        self.isAuthenticated = false
                        if let error = authenticationError as? LAError {
                            // Handle different LAError cases...
                        } else {
                            self.authenticationErrorMessage = "验证失败：\(authenticationError?.localizedDescription ?? "未知错误")"
                        }
                    }
                }
            }
        } else {
            DispatchQueue.main.async {
                self.isAuthenticating = false
                self.isAuthenticated = false
                self.authenticationErrorMessage = error?.localizedDescription ?? "设备不支持生物识别功能。"
            }
        }
    }
}



struct CustomButton1: View {
    var title: String
    var systemImage: String
    var action: () -> Void
    var isSelected: Bool

    var body: some View {
        Button(action: action) {
            ZStack {
                RoundedRectangle(cornerRadius: 10)
                    .fill(isSelected ? Color.green : Color.blue)
                    .shadow(radius: 5)
                
                HStack {
                    Image(systemName: systemImage)
                        .font(.title)
                    Text(title)
                        .fontWeight(.bold)
                }
                .padding()
                .foregroundColor(.white)
            }
            .contentShape(Rectangle()) // 使整个区域可点击
        }
        .buttonStyle(PlainButtonStyle()) // 移除默认的按钮样式影响
    }
}



// 新增的 InfoCardView 视图
struct InfoCardView: View {
    let host = NetworkConfig.fileServer.replacingOccurrences(of: "http://", with: "").replacingOccurrences(of: ":5100", with: "")
    let ipAddressReal = NetworkConfig.fileServerIP
    let port = NetworkConfig.fileServerPort
    let flaskFile = "appCalendar.py"
    
    var body: some View {
        Text("本功能依赖项：").font(.headline)
        VStack(alignment: .leading, spacing: 10) {
            Text("服务器信息").font(.headline)
            Text("主机: \(host)")
            Text("真实IP地址: \(ipAddressReal)")
            Text("端口: \(port)")
            Text("Flask文件: \(flaskFile)")
        }
        .padding()
        .background(Color.blue.opacity(0.1))
        .cornerRadius(10)
        .shadow(radius: 5)
        .padding(.horizontal)
    }
}

//主视图代码结束











extension Data {
    /// 便捷方法，将字符串转换为 Data 并追加到 Data 对象
    mutating func append(_ string: String) {
        if let data = string.data(using: .utf8) {
            append(data)
        }
    }
}

import SwiftUI // 确保导入
import Foundation // 确保导入
import Combine // 确保导入
import UniformTypeIdentifiers // 确保导入
import AppKit // 确保导入 (为了 NSWorkspace, NSOpenPanel 等)
import ZIPFoundation // 如果你之前用了，保留
import LocalAuthentication // 如果你之前用了，保留


// --- UploadManager, SlotsViewModel_20240823, FileRecord, SlotFileMapping 等结构体/类定义保持不变 ---
// (这里假设它们定义在 MonitorView 之外或同一个文件中)

class UploadManager: NSObject, ObservableObject, URLSessionTaskDelegate {
    @Published var uploadProgress: Double = 0.0
    @Published var isUploading: Bool = false
    @Published var uploadCompleted: Bool = false
    @Published var uploadError: String? = nil

    private var session: URLSession!
    private var uploadCompletion: ((Bool, Error?) -> Void)?

    override init() {
        super.init()
        let configuration = URLSessionConfiguration.default
        session = URLSession(configuration: configuration, delegate: self, delegateQueue: nil)
    }

    func uploadFile(url: URL, fileURL: URL, fileName: String, dateAdded: String, completion: @escaping (Bool, Error?) -> Void) {
        guard let uploadURL = URL(string: url.absoluteString) else {
            DispatchQueue.main.async {
                self.uploadError = "无效的上传URL"
            }
            completion(false, nil)
            return
        }
        var request = URLRequest(url: uploadURL)
        request.httpMethod = "POST"

        let boundary = UUID().uuidString
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")

        let httpBody = createBody(boundary: boundary, fileURL: fileURL, fileName: fileName, dateAdded: dateAdded)

        let task = session.uploadTask(with: request, from: httpBody)
        task.resume()

        self.uploadCompletion = completion

        DispatchQueue.main.async {
            self.isUploading = true
            self.uploadProgress = 0.0
            self.uploadCompleted = false
            self.uploadError = nil
        }
    }


    private func createBody(boundary: String, fileURL: URL, fileName: String, dateAdded: String) -> Data {
        var body = Data()

        let filename = fileURL.lastPathComponent
        let mimeType = mimeTypeForPath(path: fileURL.path)
        let fileData = try? Data(contentsOf: fileURL)

        body.append("--\(boundary)\r\n")
        body.append("Content-Disposition: form-data; name=\"file\"; filename=\"\(filename)\"\r\n")
        body.append("Content-Type: \(mimeType)\r\n\r\n")
        if let fileData = fileData {
            body.append(fileData)
        }
        body.append("\r\n")

        body.append("--\(boundary)\r\n")
        body.append("Content-Disposition: form-data; name=\"date_added\"\r\n\r\n")
        body.append("\(dateAdded)\r\n")

        body.append("--\(boundary)--\r\n")

        return body
    }

    private func mimeTypeForPath(path: String) -> String {
        let url = NSURL(fileURLWithPath: path)
        let pathExtension = url.pathExtension

        if let uti = UTTypeCreatePreferredIdentifierForTag(kUTTagClassFilenameExtension, pathExtension! as CFString, nil)?.takeRetainedValue(),
           let mimetype = UTTypeCopyPreferredTagWithClass(uti, kUTTagClassMIMEType)?.takeRetainedValue() {
            return mimetype as String
        }
        return "application/octet-stream"
    }

    func urlSession(_ session: URLSession, task: URLSessionTask, didSendBodyData bytesSent: Int64,
                    totalBytesSent: Int64, totalBytesExpectedToSend: Int64) {
        DispatchQueue.main.async {
            self.uploadProgress = Double(totalBytesSent) / Double(totalBytesExpectedToSend)
        }
    }

    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        DispatchQueue.main.async {
            self.isUploading = false
            if let error = error {
                self.uploadError = error.localizedDescription
                self.uploadCompleted = false
                self.uploadCompletion?(false, error)
            } else {
                // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 这里需要检查 HTTP 响应状态码，仅在 2xx 时认为是成功。[2024-05-26 22:50:00]
                if let response = task.response as? HTTPURLResponse, (200...299).contains(response.statusCode) {
                    self.uploadCompleted = true
                    self.uploadError = nil
                    self.uploadCompletion?(true, nil)
                } else {
                    let statusCode = (task.response as? HTTPURLResponse)?.statusCode ?? -1
                    self.uploadError = "上传失败，服务器状态码: \(statusCode)"
                    self.uploadCompleted = false
                    // 即使没有 Swift Error，如果状态码不对也应该报告失败
                    self.uploadCompletion?(false, NSError(domain: "HTTPError", code: statusCode, userInfo: [NSLocalizedDescriptionKey: "服务器返回非成功状态码"]))
                }
            }
            self.uploadCompletion = nil
        }
    }
}

// --- 其他必要的结构体/类定义 ---
class SlotsViewModel_20240823: ObservableObject {
    @Published var slots: [SlotFileMapping] = []
}

struct FileRecord: Identifiable, Decodable {
    let id: Int
    let name: String
    let size: String?
    let downloadLatestTime: String?
    let date_added:String?

    enum CodingKeys: String, CodingKey {
        case id
        case name = "file_name"
        case size = "file_size"
        case downloadLatestTime = "download_latest_time"
        case date_added = "date_added"
    }

    var sizeInKB: String {
        guard let size = size, let sizeInBytes = Int(size) else {
            return "未知大小"
        }
        return String(format: "%.2f KB", Double(sizeInBytes) / 1024.0)
    }
}

struct FileRecord_20240823: Identifiable, Decodable, Equatable {
    let id: Int
    let name: String
    let size: String?
    let downloadLatestTime: String?
    let date_added:String?

    enum CodingKeys: String, CodingKey {
        case id
        case name = "file_name"
        case size = "file_size"
        case downloadLatestTime = "download_latest_time"
        case date_added = "date_added"
    }

    var sizeInKB: String {
        guard let size = size, let sizeInBytes = Int(size) else {
            return "未知大小"
        }
        return String(format: "%.2f KB", Double(sizeInBytes) / 1024.0)
    }

    static func == (lhs: FileRecord_20240823, rhs: FileRecord_20240823) -> Bool {
        return lhs.id == rhs.id
    }
}

struct SlotFileMapping: Identifiable, Codable {
    var id: Int
    var level: Int
    var slot_index: Int
    var file_name: String?
    var file_id: Int?
}

struct CustomAlertView: View { // 保持不变
    var title: String
    var message: String
    var dismissAction: (() -> Void)? = nil

    var body: some View {
        VStack(spacing: 16) {
            Text(title)
                .font(.headline)
            Text(message)
                .font(.body)
            Button("确定") {
                dismissAction?()
            }
            .padding()
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
        .padding()
        .background(Color.white)
        .cornerRadius(10)
        .shadow(radius: 10)
    }
}

struct CustomAlertView_20240823: View { // 保持不变
    var title: String
    var message: String
    var primaryButtonTitle: String
    var secondaryButtonTitle: String?
    var additionalButtonTitle: String?
    var primaryAction: (() -> Void)?
    var secondaryAction: (() -> Void)?
    var cancelButtonTitle: String?
    var cancelAction: (() -> Void)?
    var additionalAction: (() -> Void)?

    var body: some View {
        VStack(spacing: 16) {
            Text(title)
                .font(.headline)
            Text(message)
                .font(.body)

            VStack(spacing: 8) {
                HStack(spacing: 16) {
                    Button(primaryButtonTitle) {
                        primaryAction?()
                    }
                    .frame(minWidth: 100)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)

                    if let secondaryButtonTitle = secondaryButtonTitle {
                        Button(secondaryButtonTitle) {
                            secondaryAction?()
                        }
                        .frame(minWidth: 100)
                        .padding()
                        .background(Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                }

                HStack(spacing: 16) {
                    if let additionalButtonTitle = additionalButtonTitle {
                        Button(additionalButtonTitle) {
                            additionalAction?()
                        }
                        .frame(minWidth: 100)
                        .padding()
                        .background(Color.orange)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }

                    if let cancelButtonTitle = cancelButtonTitle {
                        Button(cancelButtonTitle) {
                            cancelAction?()
                        }
                        .frame(minWidth: 100)
                        .padding()
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                }
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(10)
        .shadow(radius: 10)
    }
}


struct FileSelectionAlertView_20240823: View { // 保持不变
    var files: [FileRecord_20240823]
    var onFileSelected: (FileRecord_20240823) -> Void
    var dismissAction: () -> Void

    @Binding var selectedSlot: SlotFileMapping?
    @Binding var level1Slots: [SlotFileMapping]
    @Binding var level2Slots: [SlotFileMapping]
    @Binding var level3Slots: [SlotFileMapping]

    @State private var searchText: String = ""
    @State private var selectedFile: FileRecord_20240823?
    @FocusState private var isSearchFieldFocused: Bool

    var getSlotIndex: (SlotFileMapping) -> (level: Int, index: Int)?
    var updateSlots: () -> Void

    var filteredFiles: [FileRecord_20240823] {
        if searchText.isEmpty {
            return files
        } else {
            return files.filter { $0.name.localizedCaseInsensitiveContains(searchText) }
        }
    }

    var body: some View {
        VStack(spacing: 16) {
            Text("请选择文件")
                .font(.headline)
                .foregroundColor(.primary)

            TextField("搜索文件", text: $searchText)
                .padding(24)
                .font(.system(size: 24))
                .background(Color(NSColor.controlBackgroundColor))
                .cornerRadius(8)
                .shadow(radius: 1)
                .padding(.horizontal)
                .focused($isSearchFieldFocused)

            ScrollView {
                LazyVStack(alignment: .leading, spacing: 12) {
                    ForEach(filteredFiles, id: \.id) { file in
                        FileRowView(file: file, isSelected: selectedFile == file)
                            .onTapGesture {
                                print("选中的文件: \(file.name)")
                                selectedFile = file
                                onFileSelected(file)
                                dismissAction()
                            }
                    }
                }
                .padding(.horizontal)
            }
            .frame(height: 300)

            Button("取消") {
                print("取消按钮点击")
                dismissAction()
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(8)
            .shadow(radius: 1)
            .padding(.horizontal)
        }
        .padding(24)
        .background(Color(NSColor.windowBackgroundColor))
        .cornerRadius(12)
        .shadow(radius: 20)
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isSearchFieldFocused = true
            }
        }
    }
}

struct FileRowView: View { // 保持不变
    let file: FileRecord_20240823
    let isSelected: Bool

    var body: some View {
        Text(file.name)
            .padding(12)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(NSColor.windowBackgroundColor))
                    .shadow(radius: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.blue, lineWidth: 1)
                    .opacity(isSelected ? 1 : 0)
            )
    }
}

struct CustomProgressView: View { // 保持不变
    var message: String

    var body: some View {
        ZStack {
            Color.black.opacity(0.4)
                .edgesIgnoringSafeArea(.all)

            VStack(spacing: 20) {
                ActivityIndicator()
                Text(message)
                    .font(.headline)
                    .foregroundColor(.white)
            }
            .padding(20)
            .background(Color.gray.opacity(0.8))
            .cornerRadius(10)
        }
    }
}

struct ActivityIndicator: NSViewRepresentable { // 保持不变
    func makeNSView(context: Context) -> NSProgressIndicator {
        let indicator = NSProgressIndicator()
        indicator.style = .spinning
        indicator.startAnimation(nil)
        return indicator
    }

    func updateNSView(_ nsView: NSProgressIndicator, context: Context) {}
}

struct SlotView_20240823: View { // 保持不变
    let slot_20240823: SlotFileMapping
    let onSlotTapped_20240823: (SlotFileMapping) -> Void
    @Binding var selectedSlot: SlotFileMapping?

    var body: some View {
        ZStack {
            Rectangle()
                .fill(isSelected() ? Color.green.opacity(0.6) : (slot_20240823.file_name == nil ? Color.gray.opacity(0.4) : Color.blue.opacity(0.4)))
                .frame(width: 160, height: 160)
                .cornerRadius(32)

            VStack {
                if let fileName = slot_20240823.file_name, !fileName.isEmpty {
                    Image(systemName: "book.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.yellow)
                    Text(fileName)
                        .font(.system(size: fileName.count > 10 ? 14 : 18))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                        .padding([.leading, .trailing], 4)
                        .frame(maxWidth: 140)
                } else {
                    Text("插槽 \(slot_20240823.slot_index)")
                        .font(.system(size: 40))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .frame(maxWidth: 140)
                }
            }
        }
        .onTapGesture {
            print("SlotView tapped: \(slot_20240823.slot_index)")
            onSlotTapped_20240823(slot_20240823)
        }
    }

    private func isSelected() -> Bool {
        return selectedSlot?.id == slot_20240823.id
    }
}


// --- MonitorView ---
struct MonitorView: View {
    
    @State private var debugMessage_1716737986000: String = "Initial debug message" // 新增调试状态变量
    // --- 所有 @State, @ObservedObject, @StateObject 变量保持不变 ---
    @State private var files: [FileRecord_20240823] = []
    @State private var selectedFile: FileRecord_20240823?
    @State private var isLoading = false
    @State private var showUploadProgress = false
    @State private var uploadProgress: Double = 0
    @State private var showAlert = false
    @State private var alertMessage = ""

    @State private var selectedView: String?
    @State private var lastDownloadedFileName: String?

    @State private var isDownloadSuccessful = false
    @State private var isUploadSuccessful = false

    @State private var selectedTab = 0

    @State private var showCustomAlert = false
    @State private var customAlertTitle = ""
    @State private var customAlertMessage = ""

    @State private var files_20240823: [FileRecord_20240823] = []
    @State private var selectedFile_20240823: FileRecord_20240823?

    @State private var showFileSelectionAlert_20240823 = false

    @State private var selectedSlot_20240823: SlotFileMapping?

    @State private var showCustomAlert_20240823 = false
    @State private var alertTitle_20240823 = ""
    @State private var alertMessage_20240823 = ""
    @State private var primaryButtonTitle_20240823 = ""
    @State private var primaryAction_20240823: (() -> Void)?
    @State private var secondaryButtonTitle_20240823 = ""
    @State private var secondaryAction_20240823: (() -> Void)?

    @State var level1Slots_20240823: [SlotFileMapping] = []
    @State var level2Slots_20240823: [SlotFileMapping] = []
    @State var level3Slots_20240823: [SlotFileMapping] = []

    @StateObject private var slotsViewModel_20240823 = SlotsViewModel_20240823()

    @State private var cancelButtonTitle_20240823 = ""
    @State private var cancelAction_20240823: (() -> Void)? = nil

    @State private var isOpeningFile = false

    @State private var additionalButtonTitle_20240823: String = ""
    @State private var additionalAction_20240823: (() -> Void)? = nil
    @State private var isFileLoading = false
    @State private var currentSelectedSlot: Int? = nil

    @State private var showMonitorViewProgressView_1736 = true

    @State private var isExpanded = false


    @State private var downloadProgress: Double = 0.0 // 这个变量现在可能用不上，除非实现 URLSessionDownloadDelegate
    @State private var downloadStatusMessage: String = "" // 这个也可以考虑移除或修改用途

    @State private var selectedLevel: Int? = nil


    @State private var windowController: NSWindowController?


    @State private var printshowtitileok = false

    @State private var downloadfileformonitorselection = false


    @State private var dots: String = ""
    @State private var timer: Timer?


    @ObservedObject var uploadManager: UploadManager
    var onUploadComplete: () -> Void

    // --- body 保持不变 ---
    var body: some View {
        VStack {
            Picker("", selection: $selectedTab) {
                Text("监视器文件管理").tag(0)
                Text("常用监视器架构").tag(1)
                Text("监视器电子化").tag(2)
            }
            .pickerStyle(SegmentedPickerStyle())
            .frame(height: 60)
            .padding()

            // 比如放在 HStack 的某个地方
            Text(debugMessage_1716737986000).foregroundColor(.red)

            if selectedTab == 0 {


                VStack {
                    HStack {
                        if let selectedFile = selectedFile {
                            Button(action: {
                                downloadFile(file: selectedFile) // 调用修改后的下载函数
                            }) {
                                Label("下载文件", systemImage: "arrow.down.circle")
                                    .font(.system(size: 20))
                                    .frame(maxWidth: .infinity, minHeight: 50)
                            }
                            .buttonStyle(.borderedProminent)
                            .padding()
                        }

                        Button(action: {
                            let panel = NSOpenPanel()
                            panel.canChooseFiles = true
                            panel.canChooseDirectories = false
                            panel.allowsMultipleSelection = false
                            panel.begin { response in
                                if response == .OK, let selectedURL = panel.url {
                                    uploadFile(fileURL: selectedURL)
                                }
                            }
                        }) {
                            Label("上传文件", systemImage: "arrow.up.circle")
                                .font(.system(size: 20))
                                .frame(maxWidth: .infinity, minHeight: 50)
                        }
                        .buttonStyle(.borderedProminent)
                        .padding()
                    }

                    if uploadManager.isUploading {
                        ProgressView(value: uploadManager.uploadProgress)
                            .progressViewStyle(LinearProgressViewStyle())
                            .padding()
                        Text(String(format: "%.0f%% 上传中", uploadManager.uploadProgress * 100))
                            .font(.subheadline)
                            .foregroundColor(.gray)
                    } else if uploadManager.uploadCompleted {
                        // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 上传完成后短暂显示成功信息，然后可以考虑自动隐藏或提供清除按钮。[2024-05-26 22:50:00]
                        Text("上传完成！")
                            .foregroundColor(.green)
                            .padding()
                            .onAppear {
                                // 比如 5 秒后自动隐藏
                                DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                                    if uploadManager.uploadCompleted { // 再次检查状态，防止用户在这期间又上传了
                                         uploadManager.uploadCompleted = false // 重置状态以隐藏消息
                                    }
                                }
                            }
                    } else if let error = uploadManager.uploadError {
                        Text("上传失败: \(error)")
                            .foregroundColor(.red)
                            .padding()
                    }


                    // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 移除了旧的下载进度条，因为 URLSessionDataTask 不直接提供流式进度。[2024-05-26 22:50:00]
                    // if !downloadStatusMessage.isEmpty { ... } // 这部分代码移除

                    Spacer()

                    VStack {
                        if isLoading {
                            ProgressView("正在加载文件列表...")
                                .progressViewStyle(CircularProgressViewStyle())
                                .padding()
                        } else {
                            ScrollView {
                                LazyVGrid(columns: [GridItem(.adaptive(minimum: 160))]) {
                                    ForEach(files) { file in
                                        VStack {
                                            Label(file.name, systemImage: "doc.text")
                                                .font(.system(size: 18))
                                                .padding()
                                                .frame(maxWidth: .infinity, minHeight: 168)
                                                .background(selectedFile?.id == file.id ? Color.blue.opacity(0.2) : Color.gray.opacity(0.2))
                                                .cornerRadius(8)
                                                .onTapGesture {
                                                    DispatchQueue.main.async {
                                                        selectFile(file: file)
                                                    }
                                                }

                                            Text("上传日期: \(file.date_added ?? "未知日期")")
                                                .font(.caption)
                                                .foregroundColor(.gray)

                                            Text("大小: \(file.sizeInKB)")
                                                .font(.caption)
                                                .foregroundColor(.gray)
                                        }
                                    }
                                }
                                .padding()
                            }

                        }


                    }

                    Spacer()

                    if showCustomAlert {
                        CustomAlertView(title: customAlertTitle, message: customAlertMessage) {
                            self.showCustomAlert = false
                        }
                        .padding()
                        .background(Color.black.opacity(0.3))
                        .edgesIgnoringSafeArea(.all)
                    }

                    if showCustomAlert_20240823 {
                        CustomAlertView_20240823(
                            title: alertTitle_20240823,
                            message: alertMessage_20240823,
                            primaryButtonTitle: primaryButtonTitle_20240823,
                            secondaryButtonTitle: secondaryButtonTitle_20240823,
                            additionalButtonTitle: additionalButtonTitle_20240823,
                            primaryAction: primaryAction_20240823,
                            secondaryAction: secondaryAction_20240823,
                            cancelButtonTitle: cancelButtonTitle_20240823,
                            cancelAction: cancelAction_20240823,
                            additionalAction: additionalAction_20240823
                        )
                    }
                }
                .padding()

                .onAppear {
                    DispatchQueue.main.async {
                        loadFiles {
                            print("MonitorView appeared, 文件加载完成")
                        }
                    }
                }

                .alert(isPresented: $showAlert) {
                    // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 修改 Alert 逻辑，下载成功时仍然可以提供"打开文件"选项。[2024-05-26 22:50:00]
                    if isDownloadSuccessful { // 假设 isDownloadSuccessful 状态会被正确设置
                        return Alert(
                            title: Text("提示"),
                            message: Text(alertMessage),
                            primaryButton: .default(Text("打开文件")) {
                                if let downloadedFileName = lastDownloadedFileName {
                                    openDownloadedFile(named: downloadedFileName)
                                }
                            },
                            secondaryButton: .default(Text("确定")) {
                                // 点击确定后重置下载成功状态，避免下次普通提示也显示打开按钮
                                isDownloadSuccessful = false
                            }
                        )
                    } else {
                        // 普通提示或下载失败提示
                        return Alert(
                            title: Text("提示"),
                            message: Text(alertMessage),
                            dismissButton: .default(Text("确定"))
                        )
                    }
                }

            } else if selectedTab == 1 { // Tab 1 的内容保持不变

                VStack {
                    Text("常用的监视器")
                        .font(.largeTitle)
                        .padding(.top)

                    if printshowtitileok {

                        Text("正在下载\(dots)")
                            .font(.largeTitle)
                            .padding(.top)
                            .onAppear {
                                startAnimatingDots()
                            }
                            .onDisappear {
                                stopAnimatingDots()
                            }

                    }

                    if showMonitorViewProgressView_1736 {
                        CustomProgressView(message: "正在载入插槽数据...")
                    }

                    ZStack {
                        HStack(alignment: .top) {
                            ScrollView([.horizontal, .vertical]) {
                                VStack(spacing: 40) {
                                    ForEach(1...3, id: \.self) { level in
                                        HStack(spacing: 20) {
                                            Button(action: {
                                                withAnimation {
                                                    if self.selectedLevel == level {
                                                        self.selectedLevel = nil
                                                    } else {
                                                        self.selectedLevel = level
                                                    }
                                                }
                                            }){
                                                HStack {
                                                    Image(systemName: self.selectedLevel == level ? "chevron.down.circle" : "chevron.right.circle")
                                                        .font(.title)
                                                        .foregroundColor(.blue)
                                                    Text("\(level) 层")
                                                        .font(.title2)
                                                        .foregroundColor(.blue)
                                                }
                                            }

                                            if self.selectedLevel == level {
                                                VStack {
                                                    if level == 1 {
                                                        HStack(alignment: .top) {
                                                            Image(systemName: "exclamationmark.triangle")
                                                                .foregroundColor(.red)
                                                            Text("失败并不是因为不够聪明，而是因为未进行模拟演练，从而导致参数数据太少了，实战的时候必然会死亡。\n另外，失败和成功并非很重要，往往自身出了问题才会造成去打愤怒之仗。")
                                                                .font(.caption)
                                                                .foregroundColor(.red)
                                                                .lineLimit(nil)
                                                        }
                                                    } else if level == 2 {
                                                        HStack(alignment: .top) {
                                                            Image(systemName: "heart.text.square")
                                                                .foregroundColor(.red)
                                                            Text("在做对外实战之前，先强壮强化自身。\n能够强壮自身的方法有很多，可以开发一些。")
                                                                .font(.caption)
                                                                .foregroundColor(.red)
                                                                .lineLimit(nil)
                                                        }
                                                    } else if level == 3 {
                                                        HStack(alignment: .top) {
                                                            Image(systemName: "rectangle.grid.3x2.fill")
                                                                .foregroundColor(.red)
                                                            Text("对外实战/投资。\n实战是指有所图。\n投资是指图财。")
                                                                .font(.caption)
                                                                .foregroundColor(.red)
                                                        }
                                                    }
                                                }
                                                .padding(.top, 5)
                                            }

                                            ForEach(slotsViewModel_20240823.slots.filter { $0.level == level }) { slot in
                                                SlotView_20240823(
                                                    slot_20240823: slot,
                                                    onSlotTapped_20240823: handleSlotTapped_20240823,
                                                    selectedSlot: $selectedSlot_20240823
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                            .padding()

                            if showFileSelectionAlert_20240823, let selectedSlot = selectedSlot_20240823 {
                                FileSelectionAlertView_20240823(
                                    files: files_20240823,
                                    onFileSelected: { file in
                                        updateSlotWithFile_20240823(slot: selectedSlot, file: file)
                                        showFileSelectionAlert_20240823 = false
                                    },
                                    dismissAction: {
                                        showFileSelectionAlert_20240823 = false
                                    },
                                    selectedSlot: $selectedSlot_20240823,
                                    level1Slots: $level1Slots_20240823,
                                    level2Slots: $level2Slots_20240823,
                                    level3Slots: $level3Slots_20240823,
                                    getSlotIndex: { slot in
                                        return getSlotIndex(slot: slot, level1Slots: level1Slots_20240823, level2Slots: level2Slots_20240823, level3Slots: level3Slots_20240823)
                                    },
                                    updateSlots: {
                                        updateSlots_20240823()
                                    }
                                )
                                .zIndex(1)
                                .padding()
                                .background(Color.black.opacity(0.3))
                                .edgesIgnoringSafeArea(.all)
                            }
                        }

                        if showCustomAlert_20240823 {
                            CustomAlertView_20240823(
                                title: alertTitle_20240823,
                                message: alertMessage_20240823,
                                primaryButtonTitle: primaryButtonTitle_20240823,
                                secondaryButtonTitle: secondaryButtonTitle_20240823,
                                additionalButtonTitle: additionalButtonTitle_20240823,
                                primaryAction: primaryAction_20240823,
                                secondaryAction: secondaryAction_20240823,
                                cancelButtonTitle: cancelButtonTitle_20240823,
                                cancelAction: cancelAction_20240823,
                                additionalAction: additionalAction_20240823
                            )
                            .padding()
                            .background(Color.white.opacity(0.9))
                            .cornerRadius(10)
                            .shadow(radius: 10)
                            .zIndex(2)
                            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
                        }
                    }
                    .padding()
                    .onAppear {
                        loadSlotFileMappings {
                            showMonitorViewProgressView_1736 = false
                        }
                    }

                    Spacer()
                }




            }else if selectedTab == 2{ // Tab 2 的内容保持不变

                VStack{

                    Text("监视器电子计算机辅助")
                        .font(.largeTitle)
                        .padding(.top)

                    Button(action: openMonitorAIWindow) {
                        Text("打开 AI 监视器")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.blue)
                            .buttonStyle(.bordered)
                    }


                    Button(action: inputCalendar) {
                        Text("输入日常思绪")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.blue)
                            .buttonStyle(.bordered)
                    }



                    Button(action: {
                        showD3test()
                    }) {
                        Text("显示 D3test 全屏视图")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.blue)
                            .buttonStyle(.bordered)
                    }




                }


            }


            Spacer()
        }
        .frame(minWidth: 800, minHeight: 600)
    }

    // --- 其他辅助函数保持不变 (除了下载相关函数) ---
    func startAnimatingDots() { // 保持不变
        var count = 0
        timer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
            count = (count + 1) % 4
            dots = String(repeating: ".", count: count)
        }
    }

    func stopAnimatingDots() { // 保持不变
        timer?.invalidate()
        timer = nil
        dots = ""
    }

    func openMonitorAIWindow() { // 保持不变
        let monitorView = NSHostingController(rootView: finalMonitor_AI_version_1_0())
        let controller = FinalMonitorWindowController(rootView: monitorView.view)
        controller.showWindow(nil)
        controller.showWindowAnimated()
        self.windowController = controller
    }

    func inputCalendar() { // 保持不变
        let monitorView = NSHostingController(rootView: CalendarView())
        let controller = FinalMonitorWindowController1(rootView: monitorView.view)
        controller.showWindow(nil)
        controller.showWindowAnimated()
        self.windowController = controller
    }




    func showD3test() { // 已修改，添加返回回调
        let window = NSWindow(
            contentRect: NSScreen.main?.frame ?? NSRect(x: 0, y: 0, width: 800, height: 600),
            styleMask: [.titled, .closable, .resizable, .fullSizeContentView],
            backing: .buffered,
            defer: false
        )
        
        // 创建带有返回回调的D3test视图
        let d3testView = D3test(onReturn: {
            // 当用户点击返回时，只需要关闭窗口
            window.close()
            print("从D3test返回到主界面")
        })
        
        window.contentView = NSHostingView(rootView: d3testView)
        window.title = "任务系统 - 一图多用" // 添加标题
        window.isReleasedWhenClosed = false
        window.makeKeyAndOrderFront(nil)
        window.setFrame(NSScreen.main?.visibleFrame ?? NSRect(x: 0, y: 0, width: 800, height: 600), display: true, animate: true)
    }




    func uploadFile(fileURL: URL) {
        let fileName = fileURL.lastPathComponent
        let dateAdded = DateFormatter.localizedString(from: Date(), dateStyle: .medium, timeStyle: .short)

        uploadManager.uploadFile(url: URL(string: NetworkConfig.fileServer + NetworkConfig.Endpoints.uploadFile)!, fileURL: fileURL, fileName: fileName, dateAdded: dateAdded) { success, error in
            if success {
                print("上传成功")
                onUploadComplete()
                self.loadFiles {
                    print("MonitorView 的文件列表已刷新")
                }
            } else {
                print("上传失败: \(error?.localizedDescription ?? "未知错误")")
                // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 可以在这里也显示一个 Alert 提示上传失败 [2024-05-26 22:50:00]
                DispatchQueue.main.async {
                    self.alertMessage = "上传文件 \(fileName) 失败: \(error?.localizedDescription ?? "未知错误")"
                    self.isDownloadSuccessful = false // 确保不是下载成功状态
                    self.showAlert = true
                }
            }
        }
    }


    func isFolderFileFullyDownloaded(at fileURL: URL) -> Bool { // 保持不变
        do {
            let resourceValues = try fileURL.resourceValues(forKeys: [.ubiquitousItemDownloadingStatusKey, .isDirectoryKey])
            if let isDirectory = resourceValues.isDirectory, isDirectory {
                let fileURLs = try FileManager.default.contentsOfDirectory(at: fileURL, includingPropertiesForKeys: [.ubiquitousItemDownloadingStatusKey], options: .skipsHiddenFiles)
                for subFileURL in fileURLs {
                    if !isFolderFileFullyDownloaded(at: subFileURL) {
                        return false
                    }
                }
                return true
            } else if let downloadingStatus = resourceValues.ubiquitousItemDownloadingStatus {
                return downloadingStatus == .current
            }
        } catch {
            print("Error checking file download status: \(error.localizedDescription)")
        }
        return false
    }

    func downloadFolderFileIfNeeded(at fileURL: URL) { // 保持不变
        do {
            downloadStatusMessage = "正在下载文件: \(fileURL.lastPathComponent)" // 这个状态信息可能需要重新评估是否还需要
            try FileManager.default.startDownloadingUbiquitousItem(at: fileURL)
            // downloadProgress += 1.0 // 移除旧的进度模拟
            print("Downloading file: \(fileURL.lastPathComponent)")
        } catch {
            print("Error forcing file download: \(error.localizedDescription)")
            downloadStatusMessage = "文件下载失败: \(error.localizedDescription)"
        }
    }


    func updateSlots_20240823(){ // 保持不变
    }


    func updateSlotWithFile_20240823(slot: SlotFileMapping, file: FileRecord_20240823) { // 保持不变
        print("调用了 updateSlotWithFile_20240823, 选中的文件: \(file.name), 插槽: \(slot.slot_index), 层级: \(slot.level)")

        if let slotIndex = slotsViewModel_20240823.slots.firstIndex(where: { $0.id == slot.id }) {
            print("找到了匹配的插槽ID: \(slot.id), 层级: \(slot.level), 插槽索引: \(slot.slot_index)")
            slotsViewModel_20240823.slots[slotIndex].file_name = file.name
            slotsViewModel_20240823.slots[slotIndex].file_id = file.id
            saveSlotToDatabase(slot: SlotFileMapping(id: slot.id, level: slot.level, slot_index: slot.slot_index, file_name: file.name, file_id: file.id))
        } else {
            print("未找到匹配的插槽")
        }
    }


    func saveSlotToDatabase(slot: SlotFileMapping) { // 保持不变
        let url = URL(string: NetworkConfig.fileServer + NetworkConfig.Endpoints.saveSlotFileMapping)!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        let requestBody: [String: Any] = [
            "id": slot.id,
            "file_name": slot.file_name ?? NSNull(), // 处理可选类型，如果为nil则发送null
            "file_id": slot.file_id ?? NSNull(),   // 处理可选类型
            "level": slot.level,
            "slot_index": slot.slot_index
        ]

        // 使用 JSONSerialization 前确保值是 JSON 兼容的
        guard let httpBody = try? JSONSerialization.data(withJSONObject: requestBody, options: []) else {
             print("Error creating JSON body for saveSlotToDatabase")
             return
        }
        request.httpBody = httpBody

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("保存插槽文件映射失败: \(error.localizedDescription)")
                return
            }

            if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode != 201 {
                print("服务器返回错误状态: \(httpResponse.statusCode)")
                // 可以尝试读取响应体获取更详细错误信息
                 if let data = data, let errorMsg = String(data: data, encoding: .utf8) {
                     print("Server error message: \(errorMsg)")
                 }
                return
            }

            print("插槽文件映射保存成功")
        }.resume()
    }


    func loadSlotFileMappings(completion: @escaping () -> Void) { // 保持不变
        // 修改: 使用 mainServer (5100) 代替 fileServer (5300)
        let url = URL(string: NetworkConfig.mainServer + NetworkConfig.Endpoints.loadSlotFileMappings)!
        // 保持移除 Authorization 头
        URLSession.shared.dataTask(with: url) { data, response, error in
            guard let data = data else {
                print("加载插槽文件映射失败: \(error?.localizedDescription ?? "未知错误")")
                DispatchQueue.main.async { completion() } // 确保 completion 被调用
                return
            }

            do {
                let mappings = try JSONDecoder().decode([SlotFileMapping].self, from: data)
                DispatchQueue.main.async {
                    self.slotsViewModel_20240823.slots = mappings
                    completion()
                }
            } catch {
                print("解析插槽文件映射失败: \(error.localizedDescription)")
                 DispatchQueue.main.async { completion() } // 确保 completion 被调用
            }
        }.resume()
    }


    func showFileSelectionAlert_20240823(slot: SlotFileMapping) { // 保持不变
        selectedSlot_20240823 = slot
        DispatchQueue.main.async {
            self.loadFilesForSelection {
                DispatchQueue.main.async {
                    self.showFileSelectionAlert_20240823 = true
                }
            }
        }
    }



    func showAlertMessage_20240823( // 保持不变
        title: String,
        message: String,
        primaryButton: String,
        primaryAction: @escaping () -> Void,
        secondaryButton: String? = nil,
        secondaryAction: (() -> Void)? = nil,
        cancelButton: String? = nil,
        cancelAction: (() -> Void)? = nil,
        additionalButton: String? = nil,
        additionalAction: (() -> Void)? = nil
    ) {
        print("showAlertMessage_20240823 called with cancelButton: \(cancelButton ?? "nil")")

        self.alertTitle_20240823 = title
        self.alertMessage_20240823 = message
        self.primaryButtonTitle_20240823 = primaryButton
        self.secondaryButtonTitle_20240823 = secondaryButton ?? ""
        self.primaryAction_20240823 = primaryAction
        self.secondaryAction_20240823 = secondaryAction
        self.cancelButtonTitle_20240823 = cancelButton ?? ""
        self.cancelAction_20240823 = cancelAction
        self.additionalButtonTitle_20240823 = additionalButton ?? ""
        self.additionalAction_20240823 = additionalAction

        DispatchQueue.main.async {
            self.showCustomAlert_20240823 = true
            print("Updated State - Cancel Button Title: \(self.cancelButtonTitle_20240823), Cancel Action: \(self.cancelAction_20240823 != nil)")
        }
    }


    // --- vvv 关键修改 vvv ---
    func downloadAndOpenFile_20240823(file: FileRecord_20240823) {
        // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 显示下载动画或状态。[2024-05-26 22:50:00]
        DispatchQueue.main.async {
            self.isOpeningFile = true // 可以用来显示一个通用的"处理中"指示器
            self.printshowtitileok = true // 显示"正在下载..."动画
        }

        // 修改: 使用 mainServer (5100) 代替 fileServer (5300)
        let url = URL(string: NetworkConfig.mainServer + NetworkConfig.Endpoints.downloadFile + "?file_id=\(file.id)")!
        // 保持移除 Authorization 头
        URLSession.shared.dataTask(with: url) { data, response, error in
            // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 确保总是在主线程更新 UI 和状态变量。[2024-05-26 22:50:00]
            func finishDownload(success: Bool, message: String, downloadedFileName: String? = nil) {
                DispatchQueue.main.async {
                    self.isOpeningFile = false // 隐藏处理中指示器
                    self.printshowtitileok = false // 隐藏下载动画
                    self.alertMessage = message
                    self.isDownloadSuccessful = success // 设置下载成功状态
                    self.showAlert = true // 显示提示框
                    if success, let name = downloadedFileName {
                        self.lastDownloadedFileName = name // 保存文件名用于"打开文件"按钮
                        // 尝试直接打开文件
                         let downloadsDirectory = FileManager.default.urls(for: .downloadsDirectory, in: .userDomainMask)[0]
                         let safeDestinationURL = downloadsDirectory.appendingPathComponent(name)
                         do {
                             print("Attempting to open downloaded file at: \(safeDestinationURL.path)")
                             try NSWorkspace.shared.open(safeDestinationURL)
                         } catch {
                             print("Failed to automatically open file: \(error.localizedDescription)")
                             // 即使自动打开失败，也应该让用户通过 Alert 手动打开
                             // self.alertMessage += "\n无法自动打开文件: \(error.localizedDescription)" // 可以选择追加错误信息
                         }
                    } else {
                        self.lastDownloadedFileName = nil
                    }
                }
            }

            // 1. 检查网络错误
            if let error = error {
                print("Download Network Error: \(error.localizedDescription)")
                finishDownload(success: false, message: "下载文件失败 (Network Error): \(error.localizedDescription)")
                return
            }

            // 2. 检查响应是否有效且状态码为 200
            guard let httpResponse = response as? HTTPURLResponse else {
                print("Download Error: Invalid response")
                finishDownload(success: false, message: "下载文件失败: 无效的服务器响应")
                return
            }

            print("Download Response Status Code: \(httpResponse.statusCode)")
            guard httpResponse.statusCode == 200 else {
                var errorDetail = "未知错误"
                if let data = data, let serverError = String(data: data, encoding: .utf8) {
                    errorDetail = serverError
                }
                print("Download Error: Status Code \(httpResponse.statusCode), Detail: \(errorDetail)")
                finishDownload(success: false, message: "下载文件失败: 服务器错误 (Code: \(httpResponse.statusCode)) \(errorDetail)")
                return
            }

            // 3. 检查是否有数据
            guard let fileData = data else {
                print("Download Error: Data is nil")
                finishDownload(success: false, message: "下载文件失败: 未收到文件数据")
                return
            }
            print("Download Success: Received \(fileData.count) bytes of data.")

            // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 不再需要 JSON 解析和 Base64 解码。[2024-05-26 22:50:00]

            // 4. 尝试获取原始文件名 (从 FileRecord_20240823 中获取，因为响应头可能没有可靠的文件名)
            let fileName = file.name
            let sanitizedFileName = fileName.replacingOccurrences(of: "/", with: "-") // 保持文件名清理逻辑

            // 5. 写入文件到下载目录
            do {
                let downloadsDirectory = FileManager.default.urls(for: .downloadsDirectory, in: .userDomainMask)[0]
                let safeDestinationURL = downloadsDirectory.appendingPathComponent(sanitizedFileName)
                print("Attempting to write file to: \(safeDestinationURL.path)")
                try fileData.write(to: safeDestinationURL)
                print("File write successful.")

                // 6. 写入成功，调用完成处理函数
                // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: downloadfileformonitorselection 标志用于控制是否显示成功 Alert，现在直接触发成功流程。[2024-05-26 22:50:00]
                // downloadfileformonitorselection = true // 这个标志的逻辑可能需要重新审视，暂时按成功处理
                finishDownload(success: true, message: "文件下载成功: \(sanitizedFileName)", downloadedFileName: sanitizedFileName)

            } catch {
                print("File Write Error: \(error.localizedDescription)")
                finishDownload(success: false, message: "保存文件失败: \(error.localizedDescription)")
            }
        }.resume()
    }
    // --- ^^^ 关键修改 ^^^ ---



    func handleSlotTapped_20240823(slot: SlotFileMapping) { // 保持不变，但其调用的 downloadAndOpenFile 已更新
            guard !isFileLoading else {
                print("文件正在加载中，忽略点击事件")
                return
            }
            guard currentSelectedSlot == nil || currentSelectedSlot == slot.slot_index else {
                print("另一个插槽正在处理中，忽略点击事件")
                return
            }

            currentSelectedSlot = slot.slot_index
            print("handleSlotTapped_20240823 called for slot index: \(slot.slot_index)")
            self.selectedSlot_20240823 = slot
            isFileLoading = true

            DispatchQueue.main.async {
                let isFileNameValid = slot.file_name != nil && !slot.file_name!.isEmpty && slot.file_name!.lowercased() != "n/a" && slot.file_name!.lowercased() != "null"
                let isFileIdValid = slot.file_id != nil && slot.file_id != 0

                guard isFileNameValid, isFileIdValid else {
                    print("Slot is empty or not occupied. Showing file selection alert.")
                    self.showFileSelectionAlert_20240823(slot: slot)
                    self.isFileLoading = false
                    self.currentSelectedSlot = nil
                    return
                }

                self.loadFilesForSelection {
                    DispatchQueue.main.async {
                        print("Files loaded, count: \(self.files_20240823.count)")

                        if let fileRecord = self.files_20240823.first(where: { $0.id == slot.file_id }) { // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 优先使用 file_id 查找，更可靠。[2024-05-26 22:50:00]
                            print("File record found in files_20240823 using file_id: \(fileRecord.id)")
                            self.showAlertMessage_20240823(
                                title: "插槽文件: \(fileRecord.name)", // 在标题中显示文件名
                                message: "请选择操作",
                                primaryButton: "更换文件",
                                primaryAction: {
                                    self.showFileSelectionAlert_20240823(slot: slot)
                                },
                                secondaryButton: "打开文件",
                                secondaryAction: {
                                    // self.downloadfileformonitorselection = true // 移除这个标志的设置，让下载函数内部处理
                                    self.downloadAndOpenFile_20240823(file: fileRecord) // 调用更新后的下载函数
                                },
                                cancelButton: "隐藏本窗口",
                                cancelAction: {
                                    self.showCustomAlert_20240823 = false
                                },
                                additionalButton: "移除文件",
                                additionalAction: {
                                    self.removeFileFromSlot_20240823(slot: slot)
                                }
                            )
                        } else {
                             // 如果按 ID 找不到，再尝试按名字（可能不太可靠）
                            if let fileRecordByName = self.files_20240823.first(where: { $0.name == slot.file_name }) {
                                print("File record found in files_20240823 using file_name: \(fileRecordByName.name)")
                                // ... (显示与上面类似的 Alert) ...
                                self.showAlertMessage_20240823(
                                    title: "插槽文件: \(fileRecordByName.name)",
                                    message: "请选择操作 (注意：按文件名找到，ID 可能不匹配)",
                                    primaryButton: "更换文件", primaryAction: { self.showFileSelectionAlert_20240823(slot: slot) },
                                    secondaryButton: "打开文件", secondaryAction: { self.downloadAndOpenFile_20240823(file: fileRecordByName) },
                                    cancelButton: "隐藏本窗口", cancelAction: { self.showCustomAlert_20240823 = false },
                                    additionalButton: "移除文件", additionalAction: { self.removeFileFromSlot_20240823(slot: slot) }
                                )
                            } else {
                                print("未找到文件: ID \(slot.file_id ?? -1) 或 Name \(slot.file_name ?? "nil") 在文件列表中")
                                self.showAlertMessage_20240823(
                                    title: "错误",
                                    message: "插槽关联的文件 (ID: \(slot.file_id ?? -1), Name: \(slot.file_name ?? "nil")) 在当前文件列表中未找到。可能文件已被删除。",
                                    primaryButton: "更换文件",
                                    primaryAction: {
                                         self.showFileSelectionAlert_20240823(slot: slot)
                                    },
                                    secondaryButton: "移除关联",
                                    secondaryAction: {
                                        self.removeFileFromSlot_20240823(slot: slot)
                                    },
                                    cancelButton: "关闭",
                                    cancelAction: { self.showCustomAlert_20240823 = false }
                                )
                            }
                        }

                        self.isFileLoading = false
                        self.currentSelectedSlot = nil
                    }
                }
            }
        }


    func removeFileFromSlot_20240823(slot: SlotFileMapping) { // 保持不变
        if let slotIndex = slotsViewModel_20240823.slots.firstIndex(where: { $0.id == slot.id }) {
            slotsViewModel_20240823.slots[slotIndex].file_name = nil
            slotsViewModel_20240823.slots[slotIndex].file_id = nil

            let updatedSlot = slotsViewModel_20240823.slots[slotIndex]
            let requestBody: [String: Any?] = [ // 使用 Any? 允许 nil
                "id": updatedSlot.id,
                "file_name": nil, // 发送 null
                "file_id": nil,   // 发送 null
                "level": updatedSlot.level,
                "slot_index": updatedSlot.slot_index
            ]

            let url = URL(string: NetworkConfig.fileServer + NetworkConfig.Endpoints.saveSlotFileMapping)!
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")

            // 使用 JSONSerialization.data 时，它会自动处理字典中的 NSNull 或 nil 值
            request.httpBody = try! JSONSerialization.data(withJSONObject: requestBody, options: [])

            URLSession.shared.dataTask(with: request) { data, response, error in
                if let error = error {
                    print("Failed to remove file from slot: \(error.localizedDescription)")
                    return
                }
                if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode != 201 {
                    print("Failed to remove file from slot, server returned status: \(httpResponse.statusCode)")
                    return
                }
                print("File successfully removed from slot")
                 DispatchQueue.main.async {
                    // 可以选择在成功后刷新插槽视图
                    loadSlotFileMappings {}
                 }
            }.resume()
        }
        self.showCustomAlert_20240823 = false
    }


    func selectFileForSlot(slot: SlotFileMapping) { // 保持不变
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = false
        panel.begin { response in
            panel.close() // 确保面板关闭
            guard response == .OK, let selectedURL = panel.url else { return }
            let fileName = selectedURL.lastPathComponent

             // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 这个函数的逻辑似乎与 updateSlotWithFile_20240823 重复了，并且依赖于旧的 level1/2/3Slots 状态，建议废弃或重构。[2024-05-26 22:50:00]
             print("WARNING: selectFileForSlot function logic might be outdated or redundant.")

            // 应该调用 loadFilesForSelection 确认文件存在，然后调用 updateSlotWithFile_20240823
            // 这里暂时保留旧逻辑框架，但标记为需要检查
            if let existingFile = self.files_20240823.first(where: { $0.name == fileName }) {
                print("File \(fileName) found in existing list (ID: \(existingFile.id)). Updating slot...")
                self.updateSlotWithFile_20240823(slot: slot, file: existingFile)
            } else {
                 self.showAlertMessage_20240823(
                     title: "错误",
                     message: "选择的文件 '\(fileName)' 不在服务器文件列表中。请先上传该文件。",
                     primaryButton: "确定",
                     primaryAction: { self.showCustomAlert_20240823 = false }
                 )
             }
        }
    }


    func getSlot(level: Int, index: Int) -> SlotFileMapping? { // 保持不变
        let slot = slotsViewModel_20240823.slots.first { $0.level == level && $0.slot_index == index }
        if let slot = slot {
            // print("找到了插槽: \(slot)") // 减少冗余打印
        } else {
            print("未找到 level 为 \(level)，index 为 \(index) 的插槽")
        }
        return slot
    }


    func getSlotIndex(slot: SlotFileMapping, level1Slots: [SlotFileMapping], level2Slots: [SlotFileMapping], level3Slots: [SlotFileMapping]) -> (level: Int, index: Int)? { // 保持不变 (但依赖的 level1/2/3Slots 可能需要更新)
        // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 这个函数依赖于旧的按层级分开的数组，现在所有插槽都在 slotsViewModel_20240823.slots 中，这个函数可能不再需要或需要重写。[2024-05-26 22:50:00]
        print("WARNING: getSlotIndex function logic might be outdated based on current slotsViewModel usage.")
        // 尝试基于 slotsViewModel 查找索引
        if let index = slotsViewModel_20240823.slots.firstIndex(where: { $0.id == slot.id }) {
             return (level: slot.level, index: index) // 返回在总列表中的索引
        }

        print("未找到匹配的插槽 in getSlotIndex")
        return nil
    }


    func showAlertMessage(title: String, message: String) { // 保持不变
        self.customAlertTitle = title
        self.customAlertMessage = message
        self.showCustomAlert = true
    }


    func openDownloadedFile(named fileName: String) { // 保持不变
        let downloadsDirectory = FileManager.default.urls(for: .downloadsDirectory, in: .userDomainMask)[0]
        let fileURL = downloadsDirectory.appendingPathComponent(fileName)

        do {
            if FileManager.default.fileExists(atPath: fileURL.path) {
                print("Opening file at: \(fileURL.path)")
                try NSWorkspace.shared.open(fileURL)
            } else {
                print("File not found for opening: \(fileURL.path)")
                self.alertMessage = "文件未找到：\(fileName)"
                self.isDownloadSuccessful = false // 确保不是成功状态
                self.showAlert = true
            }
        } catch {
            print("Error opening file \(fileURL.path): \(error.localizedDescription)")
            self.alertMessage = "无法打开文件：\(error.localizedDescription)"
            self.isDownloadSuccessful = false // 确保不是成功状态
            self.showAlert = true
        }
    }

    func loadFilesForSelection(completion: @escaping () -> Void) { // 保持不变
        print("开始从服务器获取文件列表 for selection...")
        fetchFilesFromServer { fetchedFiles in // fetchFilesFromServer calls completion on background thread if successful
            DispatchQueue.main.async { // <<-- Move UI updates inside DispatchQueue.main.async
                print("获取到 \(fetchedFiles.count) 个文件 for selection")
                self.files_20240823 = fetchedFiles.map { file in
                    FileRecord_20240823(id: file.id, name: file.name, size: file.size, downloadLatestTime: file.downloadLatestTime, date_added: file.date_added)
                }
                completion() // Call original completion on main thread
            }
        }
    }


    func loadFiles(completion: @escaping () -> Void) { // 保持不变
        DispatchQueue.main.async { // Ensure isLoading is toggled on main thread
             self.isLoading = true
        }
        fetchFilesFromServer { fetchedFiles in // fetchFilesFromServer now calls its completion on background thread if successful
            DispatchQueue.main.async { // <<-- Move UI updates inside DispatchQueue.main.async
                self.files = fetchedFiles
                self.isLoading = false
                print("MonitorView 的文件列表加载完成，文件数量: \(fetchedFiles.count)")
                completion() // Call the original completion handler on the main thread
            }
        }
    }


    func fetchFilesFromServer(completion: @escaping ([FileRecord_20240823]) -> Void) { // 保持不变
        // 修改: 使用 mainServer (5100) 代替 fileServer (5300)
        let urlString = NetworkConfig.mainServer + NetworkConfig.Endpoints.getFiles
        guard let url = URL(string: urlString) else {
            print("[fetchFilesFromServer ERROR] Invalid URL string: \(urlString)")
            DispatchQueue.main.async { completion([]) }
            return
        }
        print("[fetchFilesFromServer] Requesting files from URL: \(url.absoluteString)") // Log URL

        // 保持移除 Authorization 头
        URLSession.shared.dataTask(with: url) { data, response, error in
            // 1. Check for network errors
            if let error = error {
                print("[fetchFilesFromServer ERROR] Network error: \(error.localizedDescription)")
                DispatchQueue.main.async { completion([]) }
                return
            }

            // 2. Check response status code
            if let httpResponse = response as? HTTPURLResponse {
                print("[fetchFilesFromServer] Received HTTP status code: \(httpResponse.statusCode)")
                if !(200...299).contains(httpResponse.statusCode) {
                     print("[fetchFilesFromServer ERROR] Server returned non-success status: \(httpResponse.statusCode)")
                     // Optionally log response body for non-success codes
                     if let data = data, let responseBody = String(data: data, encoding: .utf8) {
                         print("[fetchFilesFromServer ERROR] Server response body: \(responseBody)")
                     }
                     DispatchQueue.main.async { completion([]) }
                     return
                }
            } else {
                print("[fetchFilesFromServer ERROR] Invalid response received (not HTTPURLResponse).")
                DispatchQueue.main.async { completion([]) }
                return
            }

            // 3. Check if data exists
            guard let data = data else {
                print("[fetchFilesFromServer ERROR] No data received from server.")
                DispatchQueue.main.async { completion([]) }
                return
            }

            // Log raw data before decoding
            if let rawDataString = String(data: data, encoding: .utf8) {
                 print("[fetchFilesFromServer] Received raw data: \(rawDataString)")
            } else {
                 print("[fetchFilesFromServer] Received \(data.count) bytes of data (could not convert to UTF-8 string).")
            }


            // 4. Decode data
            do {
                let filesResponse = try JSONDecoder().decode([FileRecord_20240823].self, from: data)
                print("[fetchFilesFromServer] Successfully decoded \(filesResponse.count) file records.")
                completion(filesResponse) // Call completion ON THE BACKGROUND THREAD
            } catch {
                print("[fetchFilesFromServer ERROR] JSON Decoding error: \(error)") // Log specific decoding error
                // Log the problematic data again on error
                 if let rawDataString = String(data: data, encoding: .utf8) {
                     print("[fetchFilesFromServer ERROR] Raw data that failed decoding: \(rawDataString)")
                }
                DispatchQueue.main.async {
                    // Update UI on main thread for error alert
                    self.alertMessage = "解析文件列表失败: \(error.localizedDescription)"
                    self.isDownloadSuccessful = false // Ensure this is false for error alerts
                    self.showAlert = true
                    completion([]) // Call completion ON THE MAIN THREAD in case of error
                }
            }
        }.resume()
    }


    func selectFile(file: FileRecord_20240823) { // 保持不变
        self.selectedFile_20240823 = file
    }


    func downloadFile(file: FileRecord_20240823) {
        print("[Frontend DEBUG \(file.id)] downloadFile called at \(Date())") // 开始时间
        // ... (重置状态等) ...
        DispatchQueue.main.async {
             self.downloadStatusMessage = "开始下载: \(file.name)..."
             // ...
        }

        let url = URL(string: NetworkConfig.fileServer + NetworkConfig.Endpoints.downloadFile + "?file_id=\(file.id)")!
        print("[Frontend DEBUG \(file.id)] Requesting URL: \(url)")

        URLSession.shared.dataTask(with: url) { data, response, error in
            print("[Frontend DEBUG \(file.id)] URLSession callback entered at \(Date())") // 回调开始时间

            func finishDownload(success: Bool, message: String, downloadedFileName: String? = nil) {
                 print("[Frontend DEBUG \(file.id)] finishDownload called. Success: \(success), Message: \(message), File: \(downloadedFileName ?? "N/A")")
                DispatchQueue.main.async {
                    print("[Frontend DEBUG \(file.id)] Updating UI in main thread for finishDownload...")
                    // ... (之前的状态更新)
                    self.alertMessage = message
                    self.isDownloadSuccessful = success
                    print("[Frontend DEBUG \(file.id)] Setting showAlert = true")
                    self.showAlert = true // <<< 设置 showAlert
                    // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 更新调试文本以测试 UI 刷新。[2024-05-27 00:00:00]
                    self.debugMessage_1716737986000 = "FinishDownload Called! Success: \(success), Time: \(Date())"
                    // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 尝试显式告知 SwiftUI 状态已改变 (虽然理论上 @State 会自动处理)。[2024-05-26 23:55:00]
                    // self.objectWillChange.send() // 如果 MonitorView 是 ObservableObject，则取消注释此行。如果是 struct View 则不需要。

                    if success, let name = downloadedFileName {
                        self.lastDownloadedFileName = name
                    } else {
                        self.lastDownloadedFileName = nil
                    }
                    print("[Frontend DEBUG \(file.id)] UI Update in main thread complete.")
                }
            }

            // 1. 检查网络错误
            if let error = error {
                print("[Frontend DEBUG \(file.id)] Network Error: \(error.localizedDescription)")
                finishDownload(success: false, message: "下载文件 \(file.name) 失败 (Network Error): \(error.localizedDescription)")
                return
            }

            // 2. 检查响应和状态码
            guard let httpResponse = response as? HTTPURLResponse else {
                print("[Frontend DEBUG \(file.id)] Invalid response")
                finishDownload(success: false, message: "下载文件 \(file.name) 失败: 无效的服务器响应")
                return
            }
            print("[Frontend DEBUG \(file.id)] Response Status Code: \(httpResponse.statusCode)")
            guard httpResponse.statusCode == 200 else {
                // ... (错误处理) ...
                finishDownload(success: false, message: "下载文件 \(file.name) 失败: 服务器错误 (Code: \(httpResponse.statusCode))")
                return
            }

            // 3. 检查数据
            guard let fileData = data else {
                 print("[Frontend DEBUG \(file.id)] Data is nil")
                finishDownload(success: false, message: "下载文件 \(file.name) 失败: 未收到文件数据")
                return
            }
            print("[Frontend DEBUG \(file.id)] Received \(fileData.count) bytes.")

            // 4. 获取文件名并清理
            let fileName = file.name
            let sanitizedFileName = fileName.replacingOccurrences(of: "/", with: "-")

            // 5. 写入文件
            do {
                let downloadsDirectory = FileManager.default.urls(for: .downloadsDirectory, in: .userDomainMask)[0]
                let safeDestinationURL = downloadsDirectory.appendingPathComponent(sanitizedFileName)
                print("[Frontend DEBUG \(file.id)] Writing file to: \(safeDestinationURL.path)")
                let writeStartTime_1716736989000 = Date() // 写入开始时间
                try fileData.write(to: safeDestinationURL)
                let writeEndTime_1716736989000 = Date() // 写入结束时间
                print("[Frontend DEBUG \(file.id)] File write successful. Duration: \(writeEndTime_1716736989000.timeIntervalSince(writeStartTime_1716736989000))s")

                // 6. 成功完成
                finishDownload(success: true, message: "文件下载成功: \(sanitizedFileName)", downloadedFileName: sanitizedFileName)

            } catch {
                print("[Frontend DEBUG \(file.id)] File Write Error: \(error.localizedDescription)")
                finishDownload(success: false, message: "保存文件 \(sanitizedFileName) 失败: \(error.localizedDescription)")
            }
             print("[Frontend DEBUG \(file.id)] URLSession callback finished at \(Date())") // 回调结束时间
        }.resume()
         print("[Frontend DEBUG \(file.id)] URLSession task resumed (request sent).")
    }


    func isFileFullyDownloaded(at url: URL) -> Bool { // 保持不变
        do {
            let resourceValues = try url.resourceValues(forKeys: [.isRegularFileKey, .fileSizeKey])
            if let isRegularFile = resourceValues.isRegularFile, isRegularFile {
                return true
            }
            return false
        } catch {
            print("Error checking if file is downloaded: \(error)")
            return false
        }
    }

    func isFileCurrentlyDownloading(at url: URL) -> Bool { // 保持不变
        do {
            let resourceValues = try url.resourceValues(forKeys: [.ubiquitousItemIsDownloadingKey])
            return resourceValues.ubiquitousItemIsDownloading ?? false
        } catch {
            print("Error checking if file is currently downloading: \(error)")
            return false
        }
    }


    func downloadFileIfNeeded(at url: URL) { // 保持不变 (这个函数现在可能不太需要了)
        do {
            let fileCoordinator = NSFileCoordinator()
            var error: NSError?
            print("Coordinating read for downloadIfNeeded: \(url.path)") // 增加日志
            fileCoordinator.coordinate(readingItemAt: url, options: [.withoutChanges], error: &error) { (newURL) in
                do {
                     print("Attempting to read data to trigger download: \(newURL.path)")
                    _ = try Data(contentsOf: newURL)
                     print("Read data successful (download likely triggered or file exists): \(newURL.path)")
                } catch {
                    print("Error forcing file download by reading: \(error)")
                }
            }
            if let error = error {
                print("Error coordinating file access for downloadIfNeeded: \(error.localizedDescription)")
            }
        }
        //  移除 catch block, 因为 coordinate 本身会处理 error
        // catch {
        //    print("Error in downloadFileIfNeeded top level: \(error.localizedDescription)")
        // }
    }


    func compressPackageFile(at url: URL) -> Data { // 保持不变
        let fileManager = FileManager.default
        let archiveURL = fileManager.temporaryDirectory.appendingPathComponent("\(url.lastPathComponent).zip")

        do {
            if fileManager.fileExists(atPath: archiveURL.path) {
                try fileManager.removeItem(at: archiveURL)
            }

            let process = Process()
            process.executableURL = URL(fileURLWithPath: "/usr/bin/zip")
            process.arguments = ["-r", archiveURL.path, url.lastPathComponent]
            process.currentDirectoryURL = url.deletingLastPathComponent()

            try process.run()
            process.waitUntilExit()

            // deepseek R1负责火山引擎构造 和 claude3.7负责UI 和chatgpt 负责沟通 ,gemini2.5pro 负责调试。: 检查 zip 进程是否成功退出。[2024-05-26 22:50:00]
            guard process.terminationStatus == 0 else {
                 print("Error: zip process terminated with status \(process.terminationStatus)")
                 // 可以尝试读取 stderr 获取错误信息
                 return Data()
            }


            let compressedData = try Data(contentsOf: archiveURL)
            try fileManager.removeItem(at: archiveURL)

            return compressedData
        } catch {
            print("Error compressing package file: \(error)")
            return Data()
        }
    }


} // --- MonitorView 结构体结束 ---



struct CustomDisclosureView: View {
    @State private var isExpanded: Bool = false
    let title: String
    let content: () -> AnyView

    var body: some View {
        VStack(alignment: .leading) {
            HStack {
                Button(action: {
                    withAnimation {
                        isExpanded.toggle()
                    }
                }) {
                    Image(systemName: isExpanded ? "chevron.down.circle.fill" : "chevron.right.circle.fill")
                        .foregroundColor(.blue)
                }
                
                Image(systemName: title)
                    .font(.title)
                    .foregroundColor(.blue)
            }
            
            if isExpanded {
                content()
                    .transition(.opacity)
                    .padding(.leading, 20) // 保持内容和箭头对齐
            }
        }
    }
}








struct GridView: View {
    var files: [FileRecord_20240823]
    @Binding var selectedFile: FileRecord_20240823?
    var onFileClicked: (FileRecord_20240823) -> Void

    var body: some View {
        LazyVGrid(columns: [GridItem(.adaptive(minimum: 100))]) {
            ForEach(files) { file in
                VStack {
                    Text(file.name)
                        .padding()
                        .background(selectedFile?.id == file.id ? Color.blue.opacity(0.2) : Color.gray.opacity(0.2))
                        .cornerRadius(8)
                        .onTapGesture {
                            onFileClicked(file)
                        }
                    Text("大小: \(file.sizeInKB)")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
        }
        .padding()
    }
}





struct Slot_20240823: Identifiable, Hashable ,Codable{
    var id = UUID()
    var index: Int  // 添加一个index属性
    var fileName: String?
}














//自定义的文本输入框
// 自定义的文本输入框
struct CustomTextField: View {
    @Binding var text: String
    var placeholder: String
    var height: CGFloat

    var body: some View {
        ZStack(alignment: .leading) {
            if text.isEmpty {
                Text(placeholder)
                    .foregroundColor(.gray)
                    .padding(.leading, 5)
            }

            TextField("", text: $text)
                .frame(height: height)
                .padding(.leading, 5)
        }
        .padding(.vertical, 8)
        .overlay(
            RoundedRectangle(cornerRadius: 5)
                .stroke(Color.gray, lineWidth: 1)
        )
    }
}
